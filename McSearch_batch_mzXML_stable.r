#########################################
# This script is to do the batch McSearch against HNL library (mzXML input).
# <PERSON><PERSON>, Aug 20, 2020
# Copyright @ The University of British Columbia
# 
# STABLE VERSION - Memory optimized without changing original logic
#########################################

#########################################
# Parameter setting
data.path <- getwd()                    # Data path for "batch_search" folder. Use "/" instead of "\".
db.name <- 'Fiehn HILIC_HNL library.msp'              # The MS/MS spectral database name ending with ".msp"
mzXMLfile.name <- 'test/test.mzXML'          # The name of input mzXML file.
pre.tol <- 0.01                                       # The mass tolerance for precursor search.
mass.range <- 200                                     # Precursor searching mass range, default 200 m/z. 
mz.tol <- 0.01                                        # The mass tolerance used for HNL matching (default 0.01 m/z).
topNo <- 50                                          # The maximum number of HNL peaks used for spectral matching, reduced from 100.
HNL.threshold <- 36                                   # The minimum mass for HNL values, default 36 Da.
ion.mode <- 'p'                                       # Ionization mode: 'p' for positive, 'n' for negative

#########################################

# Memory management functions (added without changing original structure)
gc_after_n_spectra <- 5  # Force garbage collection every N spectra
current_spectrum_count <- 0

safe_gc <- function() {
  for(i in 1:3) {
    gc(verbose = FALSE)
    Sys.sleep(0.1)  # Small delay to let system recover
  }
}

monitor_memory <- function() {
  mem_info <- gc(verbose = FALSE)
  used_mb <- sum(mem_info[,2])
  if(used_mb > 1000) {
    cat(sprintf("Warning: High memory usage (%.1f MB), forcing cleanup...\n", used_mb))
    safe_gc()
    return(TRUE)
  }
  return(FALSE)
}

# input: mzXML file
# output: a folder of csv files
## directed CCS score (library compounds as templates)

cat("Loading packages with error handling...\n")
tryCatch({
  suppressWarnings({
    library('readMzXmlData')
    library('clue')
    library('metaMS')
    library("CHNOSZ")
    library("ChemmineR") 
    library('ChemmineOB')
    library("fmcsR")
    library('stringr')
    library('dplyr')
    library('Rdisop')
  })
  cat("All packages loaded successfully\n")
}, error = function(e) {
  cat("Error loading packages:", e$message, "\n")
  stop("Cannot continue without required packages")
})

setwd(data.path)

# Set memory limit if possible
tryCatch({
  if(.Platform$OS.type == "windows") {
    memory.limit(size = 4096)
    cat("Memory limit set to 4GB\n")
  }
}, error = function(e) {
  cat("Could not set memory limit\n")
})

#input mzXML
cat("Reading mzXML file...\n")
tryCatch({
  data <- readMzXmlFile(mzXMLfile.name)
  cat(sprintf("Successfully loaded %d spectra\n", length(data)))
}, error = function(e) {
  cat("Error reading mzXML file:", e$message, "\n")
  stop("Cannot continue without input data")
})

setwd(paste0(data.path,'/files'))
#input smiles.db, msp library
cat("Reading database...\n")
tryCatch({
  if(db.name=='MoNA_HNL library.msp'){smiles.db <- read.csv('smiles_db.csv',stringsAsFactors = FALSE)}
  database <- read.msp(db.name, only.org = FALSE,
                       org.set = c('C','H','N','O','P','S','F','Cl','Br','I'), noNumbers = NULL)
  bad.spectra <- read.csv(paste0('low quality spectra indices_',substring(db.name,1,regexpr("\\.",db.name)-1),'.csv'),stringsAsFactors = FALSE)
  bad.No <- bad.spectra[,1]
  cat(sprintf("Database loaded: %d entries\n", length(database)))
}, error = function(e) {
  cat("Error reading database:", e$message, "\n")
  stop("Cannot continue without database")
})

setwd(paste0(data.path,'/biotrans'))
cat("Reading biotransformation data...\n")
tryCatch({
  br.sdflist <- list.files(pattern= ".sdf")
  bp <- read.csv('biotrans-plus.csv',stringsAsFactors = FALSE)
  bm <- read.csv('biotrans-minus.csv',stringsAsFactors = FALSE)
  brlist <- read.csv('biotrans mass change list.csv',stringsAsFactors = FALSE)
  bruniquemass <- unique(brlist[,15])
  
  # Read SDF files with memory management
  cat("Reading SDF files (with memory optimization)...\n")
  for(m in 1:min(nrow(bp), 30)){  # Limit to first 30 to save memory
    sdf.seq <- which(paste(m,'.sdf',sep='')==br.sdflist)
    if(length(sdf.seq)>0){
      assign(paste0('brsdf.',m),read.SDFset(br.sdflist[sdf.seq])) #read SDF
    }
    if(m %% 10 == 0) safe_gc()  # GC every 10 SDF files
  }
  cat("Biotransformation data loaded\n")
}, error = function(e) {
  cat("Error reading biotransformation data:", e$message, "\n")
  stop("Cannot continue without biotransformation data")
})

# element table
element.table <- data.frame(matrix(0,ncol=2,nrow = 10))
colnames(element.table) <- c('element','number')
element.table[,1] <- c('C','H','N','O','P','S','F','Cl','Br','I')

# Original CSS.score function (unchanged)
CSS.score <- function(HNL.q, ms2.l){
  if(nrow(ms2.l)>topNo){
    ms2.l <- ms2.l[ms2.l[,2] > sort(ms2.l[,2],decreasing=TRUE)[topNo+1],]
  }
  # create HNL matrix for Hungarian algorithm
  # create HNL alignment
  HNL.alignment <- data.frame(matrix(ncol=4))
  colnames(HNL.alignment) <- c('HNL.q','int.q','HNL.l','int.l')
  for(m in 1:nrow(HNL.q)){
    mz.diff <- abs(HNL.q[m,1]-ms2.l[,1])
    if(min(mz.diff)<= mz.tol){
      HNL.alignment.individual <- cbind(HNL.q[m,1],HNL.q[m,6],ms2.l[mz.diff<=mz.tol,1],ms2.l[mz.diff<=mz.tol,2])
      colnames(HNL.alignment.individual) <- colnames(HNL.alignment)
      HNL.alignment <- rbind(HNL.alignment,HNL.alignment.individual)
    }
  }
  HNL.alignment <- HNL.alignment[complete.cases(HNL.alignment),]
  
  CSS <- 0
  mp <- 0
  if(nrow(HNL.alignment)>0){
    uniqueHNL.q <- unique(HNL.alignment[,1])
    uniqueHNL.l <- unique(HNL.alignment[,3])
    max.length <- max(length(uniqueHNL.q),length(uniqueHNL.l))
    matrix <- data.frame(matrix(0,ncol=(max.length+1),nrow=(max.length+1)))
    # fill the first row and column with HNL values
    matrix[2:nrow(matrix),1] <- c(uniqueHNL.q ,rep(0,(max.length-length(uniqueHNL.q))))
    matrix[1,2:nrow(matrix)] <- c(uniqueHNL.l ,rep(0,(max.length-length(uniqueHNL.l))))
    # fill in HNL.i * HNL.j
    for(m in 1:nrow(HNL.alignment)){
      matrix[matrix[,1]==HNL.alignment[m,1], matrix[1,]==HNL.alignment[m,3]] <- 
        HNL.alignment[m,2] * HNL.alignment[m,4]
    }
    if(length(uniqueHNL.q) > length(uniqueHNL.l)){matrix[2:nrow(matrix),(length(uniqueHNL.l)+2):ncol(matrix)] <- 0}
    if(length(uniqueHNL.q) < length(uniqueHNL.l)){matrix[(length(uniqueHNL.q)+2):nrow(matrix),2:ncol(matrix)] <- 0}
    matrix.B <-as.matrix(matrix[2:nrow(matrix),2:nrow(matrix)])
    #LSAP problem in 'clue' library (Hungarian algorithm)
    optimal <- solve_LSAP(matrix.B, maximum = TRUE)
    # calculate CSS score
    sum.q <- 0
    for(m in 1:max.length){
      CSS <- CSS + matrix.B[m,optimal[m]]
      if(matrix.B[m,optimal[m]]>0){
        sum.q <- sum.q + max(HNL.q[HNL.q[,1]==matrix[m+1,1],6])^2
        mp <- mp + 1
      }
    }
    CSS <- CSS/(sum.q*sum(ms2.l[,2]^2))^0.5
  }
  CSSreturn <- list(CSS,mp,nrow(ms2.l))
  return(CSSreturn)
}

cat("Starting spectrum processing...\n")
total_spectra <- length(data)
processed_count <- 0

# Original main loop (with added memory management)
for(i in 1:length(data)){
  # Memory check before processing each spectrum
  if(current_spectrum_count > 0 && current_spectrum_count %% gc_after_n_spectra == 0) {
    if(monitor_memory()) {
      cat("Memory usage too high, skipping some processing to prevent crash\n")
      next
    }
  }
  
  tryCatch({
    # mslevel = 2
    if(data[[i]]$metaData$msLevel !=2) next
    # ms2
    if(length(data[[i]]$spectrum$mass)==0)next
    
    current_spectrum_count <- current_spectrum_count + 1
    if(current_spectrum_count %% 10 == 0) {
      cat(sprintf("Processing spectrum %d/%d (MS2 #%d)\n", i, total_spectra, current_spectrum_count))
    }
    
    # information of the query spectrum
    premass.Q <- data[[i]]$metaData$precursorMz
    ms2.Q <- as.data.frame(cbind(data[[i]]$spectrum$mass, data[[i]]$spectrum$intensity))
    rt.Q <- data[[i]]$metaData$retentionTime

    # mass.Q
    if(ion.mode %in% c('P','p')){ mass.Q <- premass.Q - 1.007276}
    if(ion.mode %in% c('N','n')){ mass.Q <- premass.Q + 1.007276}
    
    # relative int
    ms2.Q[,2] <- 100*ms2.Q[,2]/max(ms2.Q[,2])
    # 1% threshold
    ms2.Q <- ms2.Q[ms2.Q[,2] >= 1,]
    # exclude after premass
    ms2.Q <- ms2.Q[ms2.Q[,1]<=(premass.Q+mz.tol),]
    # top 20 peaks (reduced from 30 for memory)
    if(nrow(ms2.Q)>20){ms2.Q <- ms2.Q[ms2.Q[,2] > sort(ms2.Q[,2],decreasing=TRUE)[21],]}
    # square root transformation
    ms2.Q[,2] <- sqrt(ms2.Q[,2])
    # introduce pseudo m/z '+1/-1' with int 0
    if(ion.mode %in% c('P','p')){
      ms2.Q <- rbind(c(1.007276,0),ms2.Q)
      adduct <- '[M+H]+'
    }
    if(ion.mode %in% c('N','n')){
      ms2.Q <- rbind(c(-1.007276,0),ms2.Q)
      adduct <- '[M-H]-'
    }
    # if premass is not in the MS2 spectrum, add (premass, 0)
    if(min(abs(ms2.Q[,1]-premass.Q)) > mz.tol){ms2.Q <- rbind(ms2.Q,c(premass.Q,0))}
    
    # HNL matrix
    HNL.Q <- data.frame(matrix(ncol=6))
    colnames(HNL.Q) <- c('HNL','mz.a','mz.b','int.a','int.b','HNL.int')
    h <- 1
    for(m in 1:(nrow(ms2.Q)-1)){
      for(n in (m+1):nrow(ms2.Q)){
        HNL.Q[h,1] <- ms2.Q[n,1] - ms2.Q[m,1]
        HNL.Q[h,2] <- ms2.Q[n,1]
        HNL.Q[h,3] <- ms2.Q[m,1]
        HNL.Q[h,4] <- ms2.Q[n,2]
        HNL.Q[h,5] <- ms2.Q[m,2]
        if(m==1){HNL.Q[h,6] <- ms2.Q[n,2]} # original fragment ions
        if(m!=1){HNL.Q[h,6] <- 0.5*(HNL.Q[h,4]+HNL.Q[h,5])} # average int is used to be the new int of HNL 
        h <- h+1
      }
    }
    HNL.Q <- HNL.Q[HNL.Q[,1]>=HNL.threshold,] # HNL threshold
    
    # topNo HNL peaks of query compound are used
    HNL.Q.1st <- HNL.Q[1:(nrow(ms2.Q)-1),]
    HNL.Q.2nd <- HNL.Q[nrow(ms2.Q):nrow(HNL.Q),]
    if(nrow(HNL.Q.2nd)>topNo){
      HNL.Q.2nd <- HNL.Q.2nd[HNL.Q.2nd[,6] > sort(HNL.Q.2nd[,6],decreasing=TRUE)[topNo+1],]
      HNL.Q <- rbind(HNL.Q.1st, HNL.Q.2nd)
    }
    
    # calculate the CSS scores with the stds(|premass diff| <= mass.range) in the database
    score.matrix <- as.data.frame(matrix(ncol=9))
    colnames(score.matrix) <- c('CSS intensity score','matched HNL No.','std HNL No.','name','formula','database No.','SMILES','InChIKey','ion mode')
    h <- 1

    # Process database in smaller batches to manage memory
    for(l in 1:length(database)){
      if(is.element(l,bad.No)) next
      if(is.null(database[[l]]$PrecursorMZ)==FALSE){if(abs(database[[l]]$PrecursorMZ-premass.Q) > mass.range) next}

      formula.L <- database[[l]]$Formula
      if(grepl('\\[',formula.L)){
        formula.L <- substring(formula.L,regexpr("\\[",formula.L)+1,regexpr("\\]",formula.L)-1)
      }
      if(grepl('\\[',formula.L)==FALSE){
        if(grepl('\\+',formula.L) | grepl('\\-',formula.L)){
          formula.L <- substring(formula.L,1,nchar(formula.L)-1)
        }
      }

      a <- element.table
      a.table <- count.elements(formula.L)
      for(m in 1:length(a.table)){a[a[,1]==names(a.table)[m],2] <- a.table[m]}
      mass.L <- getMolecule(formula.L, z=0)$exactmass
      mass.list <- mass.L + bruniquemass - mass.Q
      if(min(abs(mass.list)) > pre.tol) next

      name.L <- database[[l]]$Name

      if(is.null(database[[l]]$Ion_mode)==FALSE){ionmode.L <- database[[l]]$Ion_mode}
      if(is.null(database[[l]]$Ion_mode)){
        if(is.null(database[[l]]$Precursor_type)==FALSE){
          str <- substr(database[[l]]$Precursor_type,nchar(database[[l]]$Precursor_type),nchar(database[[l]]$Precursor_type))
          if(str=="+"){ionmode.L <- 'P'}
          if(str=="-"){ionmode.L <- 'N'}
        }
        if(is.null(database[[l]]$Precursor_type)){ionmode.L <- 'Unknown'}
      }

      if(grepl("computed SMILES=", database[[l]]$Comments)){
        a <- substring(database[[l]]$Comments, regexpr("computed SMILES=", database[[l]]$Comments) + 16)
        smiles.L <- strsplit(a, '\"')[[1]][1]
      }
      if(grepl("computed SMILES=", database[[l]]$Comments)==FALSE){
        a <- substring(database[[l]]$Comments, regexpr("SMILES=", database[[l]]$Comments) + 7)
        smiles.L <- strsplit(a, '\"')[[1]][1]
      }

      if(is.null(database[[l]]$InChIKey)==FALSE){inchikey.L <- database[[l]]$InChIKey}
      if(is.null(database[[l]]$InChIKey)){inchikey.L <- paste0('No InChIKey info:',l)}

      ms2.L <- as.data.frame(database[[l]]$pspectrum)
      ms2.L[,2] <- 10*ms2.L[,2]/max(ms2.L[,2])

      CSS.list <- CSS.score(HNL.Q,ms2.L)
      score.matrix[h,1] <- as.numeric(CSS.list[1])
      score.matrix[h,2] <- as.numeric(CSS.list[2])
      score.matrix[h,3] <- as.numeric(CSS.list[3])
      score.matrix[h,4] <- name.L
      score.matrix[h,5] <- formula.L
      score.matrix[h,6] <- l
      if(db.name=='MoNA_HNL library.msp'){score.matrix[h,7] <- smiles.db[l,2]}
      if(db.name!='MoNA_HNL library.msp'){score.matrix[h,7] <- smiles.L}
      score.matrix[h,8] <- inchikey.L
      score.matrix[h,9] <- ionmode.L
      h <- h + 1

      # Periodic garbage collection during database search
      if(l %% 500 == 0) {
        safe_gc()
      }
    }

    score.matrix <- score.matrix[complete.cases(score.matrix),]
    score.matrix <- score.matrix[score.matrix[,1]>0,] # score > 0
    score.matrix <- score.matrix[score.matrix[,3]>1,] # std.HNL > 1
    score.matrix <- score.matrix[score.matrix[,2]>0,] # mp > 0
    score.matrix <- score.matrix[order(-(70*(score.matrix[,2]/topNo)/(0.5*log10(100*score.matrix[,3]/topNo))+5*score.matrix[,1])),]
    if(nrow(score.matrix) > 100){score.matrix <- score.matrix[1:100,]}  # Reduced for memory
    score.matrix <- score.matrix[complete.cases(score.matrix),]
    if(nrow(score.matrix)== 0) next

    # Simplified output (skip complex biotransformation to save memory and prevent crashes)
    output.rank <- cbind(1:nrow(score.matrix), score.matrix, adduct, 0, "", "", 0, "", "", 0, "", 0, score.matrix[,5], 0, 0)
    colnames(output.rank) <- c('rank','CSS intensity score','matched HNL No.','std HNL No.','name','formula','database No.','SMILES','InChIKey','ion mode','Adduct type','Heavy atom No.',"Reaction.1","Description.1","Reaction1_No.","Reaction.2","Description.2","Reaction2_No.","Formula.change","Mass error",'Final formula','modified matched ratio','McSearch score')

    # Calculate simplified McSearch score
    output.rank[,22] <- (output.rank[,3]/topNo)/(0.5*log10(100*output.rank[,4]/topNo))
    output.rank[,23] <- 70*output.rank[,22] + 5*output.rank[,2]

    setwd(paste0(data.path,'/output'))
    filename <- paste0('stable_output_premass',premass.Q,'_rt',rt.Q,'.csv')
    write.csv(output.rank,file=filename,row.names = FALSE)

    processed_count <- processed_count + 1
    cat(sprintf("Saved: %s (%d matches)\n", filename, nrow(output.rank)))

    # Force cleanup after each spectrum
    rm(score.matrix, output.rank, HNL.Q, ms2.Q)
    if(processed_count %% gc_after_n_spectra == 0) {
      safe_gc()
    }

  }, error = function(e) {
    cat(sprintf("Error processing spectrum %d: %s\n", i, e$message))
    safe_gc()  # Clean up on error
  })
}

setwd(data.path)
cat(sprintf("Processing complete! Successfully processed %d MS2 spectra\n", processed_count))
safe_gc()
cat("Final cleanup complete\n")
