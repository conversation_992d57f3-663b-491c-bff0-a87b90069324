#########################################
# McSearch批处理器 - 分批处理所有光谱
# 解决内存问题的最终解决方案
#########################################

# 参数设置
data.path <- getwd()
db.name <- 'Fiehn HILIC_HNL library.msp'
mzXMLfile.name <- 'test/test.mzXML'
pre.tol <- 0.01
mass.range <- 200
mz.tol <- 0.01
topNo <- 30
HNL.threshold <- 36
ion.mode <- 'p'

# 批处理参数
spectra_per_batch <- 3         # 每批处理3个光谱
max_db_entries <- 3061         # 搜索全部数据库条目
restart_every_batches <- 5     # 每5批重启R进程

cat("=== McSearch批处理器 ===\n")

# 获取MS2光谱索引
get_ms2_indices <- function() {
  cat("扫描MS2光谱...\n")
  
  suppressMessages({
    library('readMzXmlData')
  })
  
  data <- readMzXmlFile(mzXMLfile.name)
  
  ms2_indices <- c()
  for(i in 1:length(data)) {
    if(data[[i]]$metaData$msLevel == 2 && length(data[[i]]$spectrum$mass) > 0) {
      ms2_indices <- c(ms2_indices, i)
    }
  }
  
  cat(sprintf("找到 %d 个MS2光谱\n", length(ms2_indices)))
  
  # 保存索引到文件
  write.csv(data.frame(index = ms2_indices), 'ms2_indices.csv', row.names = FALSE)
  
  return(ms2_indices)
}

# 创建批处理脚本
create_batch_script <- function(batch_start, batch_end, ms2_indices) {
  script_content <- sprintf('
#########################################
# 批处理脚本 - 批次 %d 到 %d
#########################################

# 参数设置
data.path <- getwd()
db.name <- "Fiehn HILIC_HNL library.msp"
mzXMLfile.name <- "test/test.mzXML"
pre.tol <- 0.01
mass.range <- 200
mz.tol <- 0.01
topNo <- 30
HNL.threshold <- 36
ion.mode <- "p"
max_db_entries <- 3061

# 目标光谱索引
target_indices <- c(%s)

cat("批次 %d-%d 开始\\n")

# 内存监控
monitor_memory <- function(step) {
  mem_info <- gc()
  used_mb <- sum(mem_info[,2])
  cat(sprintf("[内存] %%s: %%.1f MB\\n", step, used_mb))
  if(used_mb > 1000) {
    for(i in 1:3) gc()
  }
}

# 加载包
suppressMessages({
  library("readMzXmlData")
  library("clue") 
  library("metaMS")
  library("CHNOSZ")
  library("stringr")
  library("dplyr")
})

monitor_memory("包加载完成")

# 读取数据
data <- readMzXmlFile(mzXMLfile.name)
monitor_memory("mzXML读取完成")

# 读取数据库
setwd(paste0(data.path, "/files"))
database <- read.msp(db.name, only.org = FALSE,
                    org.set = c("C","H","N","O","P","S","F","Cl","Br","I"), 
                    noNumbers = NULL)
bad.spectra <- read.csv(paste0("low quality spectra indices_",
                              substring(db.name,1,regexpr("\\\\.",db.name)-1),".csv"),
                       stringsAsFactors = FALSE)
bad.No <- bad.spectra[,1]
setwd(data.path)

monitor_memory("数据库读取完成")

# 简化CSS评分函数
simple_css_score <- function(HNL.q, ms2.l) {
  if(nrow(ms2.l) > topNo) {
    ms2.l <- ms2.l[order(-ms2.l[,2]),][1:topNo,]
  }
  
  matches <- 0
  for(i in 1:nrow(HNL.q)) {
    mz_diff <- abs(HNL.q[i,1] - ms2.l[,1])
    if(min(mz_diff) <= mz.tol) {
      matches <- matches + 1
    }
  }
  
  score <- if(matches > 0 && nrow(ms2.l) > 0) matches / max(nrow(HNL.q), nrow(ms2.l)) else 0
  return(list(score, matches, nrow(ms2.l)))
}

# 处理每个光谱
for(spectrum_idx in target_indices) {
  tryCatch({
    cat(sprintf("处理光谱索引 %%d\\n", spectrum_idx))
    
    # 获取光谱信息
    premass.Q <- data[[spectrum_idx]]$metaData$precursorMz
    ms2.Q <- as.data.frame(cbind(data[[spectrum_idx]]$spectrum$mass, data[[spectrum_idx]]$spectrum$intensity))
    rt.Q <- data[[spectrum_idx]]$metaData$retentionTime
    
    # 计算质量
    if(ion.mode %%in%% c("P","p")) { 
      mass.Q <- premass.Q - 1.007276
      adduct <- "[M+H]+"
    } else { 
      mass.Q <- premass.Q + 1.007276
      adduct <- "[M-H]-"
    }
    
    # 预处理光谱
    ms2.Q[,2] <- 100 * ms2.Q[,2] / max(ms2.Q[,2])
    ms2.Q <- ms2.Q[ms2.Q[,2] >= 1,]
    ms2.Q <- ms2.Q[ms2.Q[,1] <= (premass.Q + mz.tol),]
    
    if(nrow(ms2.Q) > 20) {
      ms2.Q <- ms2.Q[order(-ms2.Q[,2]),][1:20,]
    }
    
    if(nrow(ms2.Q) < 2) next
    
    ms2.Q[,2] <- sqrt(ms2.Q[,2])
    
    # 添加伪峰
    if(ion.mode %%in%% c("P","p")) {
      ms2.Q <- rbind(c(1.007276, 0), ms2.Q)
    } else {
      ms2.Q <- rbind(c(-1.007276, 0), ms2.Q)
    }
    
    if(min(abs(ms2.Q[,1] - premass.Q)) > mz.tol) {
      ms2.Q <- rbind(ms2.Q, c(premass.Q, 0))
    }
    
    # 生成HNL矩阵
    HNL.Q <- data.frame(matrix(ncol=6))
    colnames(HNL.Q) <- c("HNL","mz.a","mz.b","int.a","int.b","HNL.int")
    h <- 1
    
    for(m in 1:(nrow(ms2.Q)-1)) {
      for(n in (m+1):nrow(ms2.Q)) {
        hnl_val <- ms2.Q[n,1] - ms2.Q[m,1]
        if(hnl_val >= HNL.threshold) {
          HNL.Q[h,1] <- hnl_val
          HNL.Q[h,2] <- ms2.Q[n,1]
          HNL.Q[h,3] <- ms2.Q[m,1]
          HNL.Q[h,4] <- ms2.Q[n,2]
          HNL.Q[h,5] <- ms2.Q[m,2]
          HNL.Q[h,6] <- if(m==1) ms2.Q[n,2] else 0.5*(ms2.Q[n,2] + ms2.Q[m,2])
          h <- h + 1
        }
      }
    }
    
    HNL.Q <- HNL.Q[complete.cases(HNL.Q),]
    if(nrow(HNL.Q) == 0) next
    
    if(nrow(HNL.Q) > topNo) {
      HNL.Q <- HNL.Q[order(-HNL.Q[,6]),][1:topNo,]
    }
    
    # 搜索数据库
    results <- data.frame()
    
    for(l in 1:min(length(database), max_db_entries)) {
      if(l %%in%% bad.No) next
      
      if(!is.null(database[[l]]$PrecursorMZ)) {
        if(abs(database[[l]]$PrecursorMZ - premass.Q) > mass.range) next
      }
      
      name.L <- ifelse(is.null(database[[l]]$Name), paste("Entry", l), database[[l]]$Name)
      formula.L <- ifelse(is.null(database[[l]]$Formula), "", database[[l]]$Formula)
      
      if(is.null(database[[l]]$pspectrum) || nrow(database[[l]]$pspectrum) == 0) next
      
      ms2.L <- as.data.frame(database[[l]]$pspectrum)
      if(nrow(ms2.L) == 0) next
      
      ms2.L[,2] <- 10 * ms2.L[,2] / max(ms2.L[,2])
      
      css_result <- simple_css_score(HNL.Q, ms2.L)
      
      if(css_result[[1]] > 0) {
        result_row <- data.frame(
          score = css_result[[1]],
          matches = css_result[[2]],
          lib_peaks = css_result[[3]],
          name = name.L,
          formula = formula.L,
          db_index = l,
          stringsAsFactors = FALSE
        )
        results <- rbind(results, result_row)
      }
    }
    
    if(nrow(results) > 0) {
      results <- results[order(-results$score),]
      if(nrow(results) > 20) results <- results[1:20,]
      
      setwd(paste0(data.path, "/output"))
      filename <- paste0("batch_output_premass", round(premass.Q, 5), "_rt", round(rt.Q, 2), ".csv")
      write.csv(results, file = filename, row.names = FALSE)
      setwd(data.path)
      
      cat(sprintf("保存: %%s (%%d 匹配)\\n", filename, nrow(results)))
    }
    
    gc()
    
  }, error = function(e) {
    cat(sprintf("处理光谱 %%d 时出错: %%s\\n", spectrum_idx, e$message))
  })
}

cat("批次完成\\n")
monitor_memory("批次结束")
', batch_start, batch_end, paste(ms2_indices[batch_start:batch_end], collapse = ","), batch_start, batch_end)

  return(script_content)
}

# 主函数
main <- function() {
  # 获取MS2光谱索引
  ms2_indices <- get_ms2_indices()
  
  if(length(ms2_indices) == 0) {
    cat("未找到MS2光谱\n")
    return()
  }
  
  # 计算批次数量
  total_batches <- ceiling(length(ms2_indices) / spectra_per_batch)
  cat(sprintf("将分 %d 批处理，每批 %d 个光谱\n", total_batches, spectra_per_batch))
  
  # 创建并运行批次
  for(batch_num in 1:total_batches) {
    batch_start <- (batch_num - 1) * spectra_per_batch + 1
    batch_end <- min(batch_num * spectra_per_batch, length(ms2_indices))
    
    cat(sprintf("\\n=== 准备批次 %d/%d (光谱 %d-%d) ===\\n", batch_num, total_batches, batch_start, batch_end))
    
    # 创建批处理脚本
    script_content <- create_batch_script(batch_start, batch_end, ms2_indices)
    script_filename <- paste0("batch_", batch_num, ".r")
    
    writeLines(script_content, script_filename)
    
    # 运行批处理脚本
    cat(sprintf("运行批次 %d...\\n", batch_num))
    
    tryCatch({
      system(paste("Rscript", script_filename), wait = TRUE)
      cat(sprintf("批次 %d 完成\\n", batch_num))
      
      # 删除临时脚本
      file.remove(script_filename)
      
    }, error = function(e) {
      cat(sprintf("批次 %d 出错: %s\\n", batch_num, e$message))
    })
    
    # 每几个批次后暂停，让系统恢复
    if(batch_num %% restart_every_batches == 0 && batch_num < total_batches) {
      cat("暂停5秒让系统恢复...\\n")
      Sys.sleep(5)
    }
  }
  
  # 清理
  if(file.exists("ms2_indices.csv")) {
    file.remove("ms2_indices.csv")
  }
  
  cat("\\n=== 所有批次处理完成 ===\\n")
  
  # 统计结果
  output_files <- list.files("output", pattern = "batch_output_.*\\.csv", full.names = FALSE)
  cat(sprintf("生成了 %d 个结果文件\\n", length(output_files)))
  
  if(length(output_files) > 0) {
    cat("结果文件:\\n")
    for(file in output_files) {
      cat(sprintf("  %s\\n", file))
    }
  }
}

# 运行主程序
main()
