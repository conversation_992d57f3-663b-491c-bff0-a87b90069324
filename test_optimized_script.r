#########################################
# 测试优化版本的McSearch脚本
# 这个脚本用于验证优化版本是否能正常运行
#########################################

cat("开始测试优化版本的McSearch脚本...\n")

# 检查内存使用情况
check_memory <- function(step) {
  mem_info <- gc()
  used_mb <- sum(mem_info[,2])
  cat(sprintf("[内存检查] %s: %.1f MB\n", step, used_mb))
  return(used_mb)
}

# 检查必要文件是否存在
check_files <- function() {
  cat("检查必要文件...\n")
  
  files_to_check <- c(
    "test/test.mzXML",
    "files/Fiehn HILIC_HNL library.msp",
    "files/low quality spectra indices_Fiehn HILIC_HNL library.csv",
    "biotrans/biotrans-plus.csv",
    "biotrans/biotrans-minus.csv",
    "biotrans/biotrans mass change list.csv"
  )
  
  missing_files <- c()
  for(file in files_to_check) {
    if(!file.exists(file)) {
      missing_files <- c(missing_files, file)
    } else {
      cat(sprintf("✓ %s 存在\n", file))
    }
  }
  
  if(length(missing_files) > 0) {
    cat("缺少以下文件:\n")
    for(file in missing_files) {
      cat(sprintf("✗ %s\n", file))
    }
    return(FALSE)
  }
  
  return(TRUE)
}

# 检查R包是否已安装
check_packages <- function() {
  cat("检查R包...\n")
  
  required_packages <- c(
    "readMzXmlData", "clue", "metaMS", "CHNOSZ", 
    "ChemmineR", "ChemmineOB", "fmcsR", "stringr", 
    "dplyr", "Rdisop"
  )
  
  missing_packages <- c()
  for(pkg in required_packages) {
    if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
      missing_packages <- c(missing_packages, pkg)
    } else {
      cat(sprintf("✓ %s 已安装\n", pkg))
    }
  }
  
  if(length(missing_packages) > 0) {
    cat("缺少以下R包:\n")
    for(pkg in missing_packages) {
      cat(sprintf("✗ %s\n", pkg))
    }
    cat("\n请使用以下命令安装缺少的包:\n")
    cat("install.packages(c(", paste(paste0("'", missing_packages, "'"), collapse = ", "), "))\n")
    return(FALSE)
  }
  
  return(TRUE)
}

# 测试mzXML文件读取
test_mzxml_reading <- function() {
  cat("测试mzXML文件读取...\n")
  
  tryCatch({
    library('readMzXmlData')
    data <- readMzXmlFile('test/test.mzXML')
    
    cat(sprintf("成功读取 %d 个光谱\n", length(data)))
    
    # 检查MS2光谱数量
    ms2_count <- sum(sapply(data, function(x) x$metaData$msLevel == 2 && length(x$spectrum$mass) > 0))
    cat(sprintf("找到 %d 个有效的MS2光谱\n", ms2_count))
    
    if(ms2_count == 0) {
      cat("警告：未找到有效的MS2光谱\n")
      return(FALSE)
    }
    
    # 显示前几个MS2光谱的信息
    ms2_found <- 0
    for(i in 1:min(20, length(data))) {
      if(data[[i]]$metaData$msLevel == 2 && length(data[[i]]$spectrum$mass) > 0) {
        ms2_found <- ms2_found + 1
        cat(sprintf("MS2光谱 %d: 前体质量 %.4f, 峰数 %d, 保留时间 %.2f\n", 
                   i, data[[i]]$metaData$precursorMz, 
                   length(data[[i]]$spectrum$mass), 
                   data[[i]]$metaData$retentionTime))
        if(ms2_found >= 3) break
      }
    }
    
    rm(data)
    gc()
    return(TRUE)
    
  }, error = function(e) {
    cat(sprintf("读取mzXML文件时出错: %s\n", e$message))
    return(FALSE)
  })
}

# 测试数据库读取
test_database_reading <- function() {
  cat("测试数据库读取...\n")
  
  tryCatch({
    library('metaMS')
    
    # 切换到files目录
    original_wd <- getwd()
    setwd('files')
    
    database <- read.msp('Fiehn HILIC_HNL library.msp', only.org = FALSE,
                        org.set = c('C','H','N','O','P','S','F','Cl','Br','I'), 
                        noNumbers = NULL)
    
    cat(sprintf("成功读取数据库，包含 %d 个条目\n", length(database)))
    
    # 检查前几个条目
    for(i in 1:min(3, length(database))) {
      cat(sprintf("条目 %d: %s, 分子式: %s\n", 
                 i, 
                 ifelse(is.null(database[[i]]$Name), "未知", database[[i]]$Name),
                 ifelse(is.null(database[[i]]$Formula), "未知", database[[i]]$Formula)))
    }
    
    # 检查低质量光谱索引
    bad.spectra <- read.csv('low quality spectra indices_Fiehn HILIC_HNL library.csv', stringsAsFactors = FALSE)
    cat(sprintf("低质量光谱索引包含 %d 个条目\n", nrow(bad.spectra)))
    
    setwd(original_wd)
    rm(database, bad.spectra)
    gc()
    return(TRUE)
    
  }, error = function(e) {
    cat(sprintf("读取数据库时出错: %s\n", e$message))
    setwd(original_wd)
    return(FALSE)
  })
}

# 主测试函数
run_tests <- function() {
  cat("McSearch优化版本测试\n")
  cat("====================\n")
  
  initial_memory <- check_memory("测试开始")
  
  # 检查文件
  if(!check_files()) {
    cat("文件检查失败，无法继续测试\n")
    return(FALSE)
  }
  
  # 检查包
  if(!check_packages()) {
    cat("R包检查失败，无法继续测试\n")
    return(FALSE)
  }
  
  check_memory("包加载完成")
  
  # 测试mzXML读取
  if(!test_mzxml_reading()) {
    cat("mzXML文件读取测试失败\n")
    return(FALSE)
  }
  
  check_memory("mzXML测试完成")
  
  # 测试数据库读取
  if(!test_database_reading()) {
    cat("数据库读取测试失败\n")
    return(FALSE)
  }
  
  final_memory <- check_memory("所有测试完成")
  
  cat("\n测试总结:\n")
  cat("=========\n")
  cat("✓ 所有必要文件存在\n")
  cat("✓ 所有必要R包已安装\n")
  cat("✓ mzXML文件可以正常读取\n")
  cat("✓ 数据库可以正常读取\n")
  cat(sprintf("内存使用: 从 %.1f MB 增加到 %.1f MB\n", initial_memory, final_memory))
  
  cat("\n建议:\n")
  cat("=====\n")
  cat("1. 优化版本脚本应该可以正常运行\n")
  cat("2. 如果仍然遇到内存问题，可以进一步减少chunk_size参数\n")
  cat("3. 可以增加R的内存限制: memory.limit(size = 4096)\n")
  
  return(TRUE)
}

# 运行测试
run_tests()
