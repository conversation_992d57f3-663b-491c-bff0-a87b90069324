#########################################
# McSearch 智能批处理版本
# 自动分批处理所有光谱，避免内存问题
#########################################

# 参数设置
data.path <- getwd()
db.name <- 'Fiehn HILIC_HNL library.msp'
mzXMLfile.name <- 'test/test.mzXML'
pre.tol <- 0.01
mass.range <- 200
mz.tol <- 0.01
topNo <- 50
HNL.threshold <- 36
ion.mode <- 'p'

# 批处理参数
SPECTRA_PER_BATCH <- 5         # 每批处理5个光谱
DB_ENTRIES_PER_BATCH <- 500    # 每次搜索500个数据库条目
MAX_MEMORY_MB <- 900           # 内存阈值

cat("=== McSearch 智能批处理系统 ===\n")

# 检查是否存在进度文件
progress_file <- "batch_progress.txt"
completed_spectra <- c()

if(file.exists(progress_file)) {
  completed_data <- read.table(progress_file, header = FALSE, stringsAsFactors = FALSE)
  completed_spectra <- completed_data$V1
  cat(sprintf("发现进度文件，已完成 %d 个光谱\n", length(completed_spectra)))
} else {
  cat("开始新的批处理任务\n")
}

# 内存管理函数
force_gc <- function() {
  for(i in 1:3) {
    gc(verbose = FALSE)
    Sys.sleep(0.1)
  }
}

check_memory <- function(step) {
  mem_info <- gc(verbose = FALSE)
  used_mb <- sum(mem_info[,2])
  cat(sprintf("[内存] %s: %.1f MB\n", step, used_mb))
  
  if(used_mb > MAX_MEMORY_MB) {
    cat("内存使用过高，需要重启批次\n")
    return(TRUE)
  }
  return(FALSE)
}

# 保存进度
save_progress <- function(spectrum_index) {
  write.table(spectrum_index, progress_file, append = TRUE, 
              row.names = FALSE, col.names = FALSE, quote = FALSE)
}

# 加载包
cat("加载R包...\n")
essential_packages <- c('readMzXmlData', 'clue', 'metaMS', 'CHNOSZ', 'stringr')

for(pkg in essential_packages) {
  suppressMessages(library(pkg, character.only = TRUE))
}

check_memory("包加载完成")

# 读取数据
cat("读取mzXML文件...\n")
data <- readMzXmlFile(mzXMLfile.name)
cat(sprintf("读取了 %d 个光谱\n", length(data)))

# 找到所有MS2光谱
ms2_indices <- c()
for(i in 1:length(data)) {
  if(data[[i]]$metaData$msLevel == 2 && length(data[[i]]$spectrum$mass) > 0) {
    ms2_indices <- c(ms2_indices, i)
  }
}

cat(sprintf("找到 %d 个MS2光谱\n", length(ms2_indices)))

# 过滤已完成的光谱
remaining_indices <- setdiff(ms2_indices, completed_spectra)
cat(sprintf("剩余 %d 个光谱需要处理\n", length(remaining_indices)))

if(length(remaining_indices) == 0) {
  cat("所有光谱已处理完成！\n")
  if(file.exists(progress_file)) file.remove(progress_file)
  quit(save = "no")
}

check_memory("数据读取完成")

# 读取数据库
cat("读取数据库...\n")
setwd(paste0(data.path, '/files'))

database <- read.msp(db.name, only.org = FALSE,
                    org.set = c('C','H','N','O','P','S','F','Cl','Br','I'), 
                    noNumbers = NULL)

bad.spectra <- read.csv(paste0('low quality spectra indices_',
                              substring(db.name,1,regexpr("\\.",db.name)-1),'.csv'),
                       stringsAsFactors = FALSE)
bad.No <- bad.spectra[,1]

cat(sprintf("数据库: %d 条目\n", length(database)))
check_memory("数据库读取完成")

setwd(data.path)

# 元素表
element.table <- data.frame(matrix(0,ncol=2,nrow = 10))
colnames(element.table) <- c('element','number')
element.table[,1] <- c('C','H','N','O','P','S','F','Cl','Br','I')

# CSS评分函数（原版）
CSS.score <- function(HNL.q, ms2.l){
  if(nrow(ms2.l)>topNo){
    ms2.l <- ms2.l[ms2.l[,2] > sort(ms2.l[,2],decreasing=TRUE)[topNo+1],]
  }
  
  HNL.alignment <- data.frame(matrix(ncol=4))
  colnames(HNL.alignment) <- c('HNL.q','int.q','HNL.l','int.l')
  for(m in 1:nrow(HNL.q)){
    mz.diff <- abs(HNL.q[m,1]-ms2.l[,1])
    if(min(mz.diff)<= mz.tol){
      HNL.alignment.individual <- cbind(HNL.q[m,1],HNL.q[m,6],ms2.l[mz.diff<=mz.tol,1],ms2.l[mz.diff<=mz.tol,2])
      colnames(HNL.alignment.individual) <- colnames(HNL.alignment)
      HNL.alignment <- rbind(HNL.alignment,HNL.alignment.individual)
    }
  }
  HNL.alignment <- HNL.alignment[complete.cases(HNL.alignment),]
  
  CSS <- 0
  mp <- 0
  if(nrow(HNL.alignment)>0){
    uniqueHNL.q <- unique(HNL.alignment[,1])
    uniqueHNL.l <- unique(HNL.alignment[,3])
    max.length <- max(length(uniqueHNL.q),length(uniqueHNL.l))
    matrix <- data.frame(matrix(0,ncol=(max.length+1),nrow=(max.length+1)))
    
    matrix[2:nrow(matrix),1] <- c(uniqueHNL.q ,rep(0,(max.length-length(uniqueHNL.q))))
    matrix[1,2:nrow(matrix)] <- c(uniqueHNL.l ,rep(0,(max.length-length(uniqueHNL.l))))
    
    for(m in 1:nrow(HNL.alignment)){
      matrix[matrix[,1]==HNL.alignment[m,1], matrix[1,]==HNL.alignment[m,3]] <- 
        HNL.alignment[m,2] * HNL.alignment[m,4]
    }
    if(length(uniqueHNL.q) > length(uniqueHNL.l)){matrix[2:nrow(matrix),(length(uniqueHNL.l)+2):ncol(matrix)] <- 0}
    if(length(uniqueHNL.q) < length(uniqueHNL.l)){matrix[(length(uniqueHNL.q)+2):nrow(matrix),2:ncol(matrix)] <- 0}
    matrix.B <-as.matrix(matrix[2:nrow(matrix),2:nrow(matrix)])
    
    optimal <- solve_LSAP(matrix.B, maximum = TRUE)
    
    sum.q <- 0
    for(m in 1:max.length){
      CSS <- CSS + matrix.B[m,optimal[m]]
      if(matrix.B[m,optimal[m]]>0){
        sum.q <- sum.q + max(HNL.q[HNL.q[,1]==matrix[m+1,1],6])^2
        mp <- mp + 1
      }
    }
    CSS <- CSS/(sum.q*sum(ms2.l[,2]^2))^0.5
  }
  CSSreturn <- list(CSS,mp,nrow(ms2.l))
  return(CSSreturn)
}

# 批处理主循环
cat("开始批处理...\n")
batch_count <- 0
total_processed <- 0

for(batch_start in seq(1, length(remaining_indices), by = SPECTRA_PER_BATCH)) {
  batch_end <- min(batch_start + SPECTRA_PER_BATCH - 1, length(remaining_indices))
  current_batch <- remaining_indices[batch_start:batch_end]
  batch_count <- batch_count + 1
  
  cat(sprintf("\n=== 批次 %d: 处理光谱 %d-%d ===\n", batch_count, batch_start, batch_end))
  
  # 检查内存
  if(check_memory("批次开始前")) {
    cat("内存不足，创建重启脚本...\n")
    restart_script <- sprintf('
# 自动重启脚本
cat("重启McSearch批处理...\\n")
source("McSearch_batch_smart.r")
')
    writeLines(restart_script, "restart_batch.r")
    cat("请运行: Rscript restart_batch.r\n")
    quit(save = "no")
  }
  
  # 处理当前批次的光谱
  for(spec_idx in 1:length(current_batch)) {
    i <- current_batch[spec_idx]
    
    cat(sprintf("处理光谱 %d/%d (索引 %d)\n", spec_idx, length(current_batch), i))
    
    tryCatch({
      # 获取光谱信息
      premass.Q <- data[[i]]$metaData$precursorMz
      ms2.Q <- as.data.frame(cbind(data[[i]]$spectrum$mass, data[[i]]$spectrum$intensity))
      rt.Q <- data[[i]]$metaData$retentionTime
      
      # 计算质量
      if(ion.mode %in% c('P','p')) { 
        mass.Q <- premass.Q - 1.007276
        adduct <- '[M+H]+'
      } else { 
        mass.Q <- premass.Q + 1.007276
        adduct <- '[M-H]-'
      }
      
      # 预处理光谱
      ms2.Q[,2] <- 100*ms2.Q[,2]/max(ms2.Q[,2])
      ms2.Q <- ms2.Q[ms2.Q[,2] >= 1,]
      ms2.Q <- ms2.Q[ms2.Q[,1]<=(premass.Q+mz.tol),]
      if(nrow(ms2.Q)>25){ms2.Q <- ms2.Q[ms2.Q[,2] > sort(ms2.Q[,2],decreasing=TRUE)[26],]}
      ms2.Q[,2] <- sqrt(ms2.Q[,2])
      
      if(ion.mode %in% c('P','p')){
        ms2.Q <- rbind(c(1.007276,0),ms2.Q)
      } else {
        ms2.Q <- rbind(c(-1.007276,0),ms2.Q)
      }
      
      if(min(abs(ms2.Q[,1]-premass.Q)) > mz.tol){ms2.Q <- rbind(ms2.Q,c(premass.Q,0))}
      
      # HNL矩阵
      HNL.Q <- data.frame(matrix(ncol=6))
      colnames(HNL.Q) <- c('HNL','mz.a','mz.b','int.a','int.b','HNL.int')
      h <- 1
      for(m in 1:(nrow(ms2.Q)-1)){
        for(n in (m+1):nrow(ms2.Q)){
          HNL.Q[h,1] <- ms2.Q[n,1] - ms2.Q[m,1]
          HNL.Q[h,2] <- ms2.Q[n,1]
          HNL.Q[h,3] <- ms2.Q[m,1]
          HNL.Q[h,4] <- ms2.Q[n,2]
          HNL.Q[h,5] <- ms2.Q[m,2]
          if(m==1){HNL.Q[h,6] <- ms2.Q[n,2]}
          if(m!=1){HNL.Q[h,6] <- 0.5*(HNL.Q[h,4]+HNL.Q[h,5])}
          h <- h+1
        }
      }
      HNL.Q <- HNL.Q[HNL.Q[,1]>=HNL.threshold,]
      
      HNL.Q.1st <- HNL.Q[1:(nrow(ms2.Q)-1),]
      HNL.Q.2nd <- HNL.Q[nrow(ms2.Q):nrow(HNL.Q),]
      if(nrow(HNL.Q.2nd)>topNo){
        HNL.Q.2nd <- HNL.Q.2nd[HNL.Q.2nd[,6] > sort(HNL.Q.2nd[,6],decreasing=TRUE)[topNo+1],]
        HNL.Q <- rbind(HNL.Q.1st, HNL.Q.2nd)
      }
      
      # 分批搜索数据库
      score.matrix <- data.frame()
      
      for(db_start in seq(1, length(database), by = DB_ENTRIES_PER_BATCH)) {
        db_end <- min(db_start + DB_ENTRIES_PER_BATCH - 1, length(database))
        
        temp_scores <- data.frame(matrix(ncol=9))
        colnames(temp_scores) <- c('CSS intensity score','matched HNL No.','std HNL No.','name','formula','database No.','SMILES','InChIKey','ion mode')
        h <- 1
        
        for(l in db_start:db_end){
          if(is.element(l,bad.No)) next
          if(is.null(database[[l]]$PrecursorMZ)==FALSE){if(abs(database[[l]]$PrecursorMZ-premass.Q) > mass.range) next}
          
          formula.L <- database[[l]]$Formula
          if(grepl('\\[',formula.L)){
            formula.L <- substring(formula.L,regexpr("\\[",formula.L)+1,regexpr("\\]",formula.L)-1)
          }
          if(grepl('\\[',formula.L)==FALSE){
            if(grepl('\\+',formula.L) | grepl('\\-',formula.L)){
              formula.L <- substring(formula.L,1,nchar(formula.L)-1)
            }
          }
          
          mass_check_ok <- tryCatch({
            a <- element.table
            a.table <- count.elements(formula.L)
            for(m in 1:length(a.table)){a[a[,1]==names(a.table)[m],2] <- a.table[m]}
            mass.L <- getMolecule(formula.L, z=0)$exactmass
            if(abs(mass.L - mass.Q) > pre.tol) return(FALSE)
            return(TRUE)
          }, error = function(e) return(FALSE))

          if(!mass_check_ok) next
          
          name.L <- database[[l]]$Name
          
          if(is.null(database[[l]]$Ion_mode)==FALSE){ionmode.L <- database[[l]]$Ion_mode}
          else if(is.null(database[[l]]$Precursor_type)==FALSE){
            str <- substr(database[[l]]$Precursor_type,nchar(database[[l]]$Precursor_type),nchar(database[[l]]$Precursor_type))
            if(str=="+"){ionmode.L <- 'P'} else if(str=="-"){ionmode.L <- 'N'} else {ionmode.L <- 'Unknown'}
          } else {ionmode.L <- 'Unknown'}
          
          if(grepl("computed SMILES=", database[[l]]$Comments)){
            a <- substring(database[[l]]$Comments, regexpr("computed SMILES=", database[[l]]$Comments) + 16)
            smiles.L <- strsplit(a, '\"')[[1]][1]
          } else if(grepl("SMILES=", database[[l]]$Comments)){
            a <- substring(database[[l]]$Comments, regexpr("SMILES=", database[[l]]$Comments) + 7)
            smiles.L <- strsplit(a, '\"')[[1]][1]
          } else {smiles.L <- ""}
          
          if(is.null(database[[l]]$InChIKey)==FALSE){inchikey.L <- database[[l]]$InChIKey}
          else {inchikey.L <- paste0('No InChIKey info:',l)}
          
          ms2.L <- as.data.frame(database[[l]]$pspectrum)
          ms2.L[,2] <- 10*ms2.L[,2]/max(ms2.L[,2])
          
          CSS.list <- CSS.score(HNL.Q,ms2.L)
          temp_scores[h,1] <- as.numeric(CSS.list[1])
          temp_scores[h,2] <- as.numeric(CSS.list[2])
          temp_scores[h,3] <- as.numeric(CSS.list[3])
          temp_scores[h,4] <- name.L
          temp_scores[h,5] <- formula.L
          temp_scores[h,6] <- l
          temp_scores[h,7] <- smiles.L
          temp_scores[h,8] <- inchikey.L
          temp_scores[h,9] <- ionmode.L
          h <- h + 1
        }
        
        temp_scores <- temp_scores[complete.cases(temp_scores),]
        score.matrix <- rbind(score.matrix, temp_scores)
        
        # 每处理一批数据库条目后清理内存
        if(db_end %% (DB_ENTRIES_PER_BATCH * 2) == 0) {
          force_gc()
        }
      }
      
      if(nrow(score.matrix) > 0) {
        score.matrix <- score.matrix[score.matrix[,1]>0,]
        score.matrix <- score.matrix[score.matrix[,3]>1,]
        score.matrix <- score.matrix[score.matrix[,2]>0,]
        score.matrix <- score.matrix[order(-(70*(score.matrix[,2]/topNo)/(0.5*log10(100*score.matrix[,3]/topNo))+5*score.matrix[,1])),]
        if(nrow(score.matrix) > 100){score.matrix <- score.matrix[1:100,]}
        
        # 简化输出（跳过生物转化处理）
        output.rank <- cbind(1:nrow(score.matrix), score.matrix, adduct, 0, "", "", 0, "", "", 0, "", 0, score.matrix[,5], 0, 0)
        colnames(output.rank) <- c('rank','CSS intensity score','matched HNL No.','std HNL No.','name','formula','database No.','SMILES','InChIKey','ion mode','Adduct type','Heavy atom No.',"Reaction.1","Description.1","Reaction1_No.","Reaction.2","Description.2","Reaction2_No.","Formula.change","Mass error",'Final formula','modified matched ratio','McSearch score')
        
        output.rank[,22] <- (output.rank[,3]/topNo)/(0.5*log10(100*output.rank[,4]/topNo))
        output.rank[,23] <- 70*output.rank[,22] + 5*output.rank[,2]
        
        setwd(paste0(data.path,'/output'))
        filename <- paste0('batch_output_premass',premass.Q,'_rt',rt.Q,'.csv')
        write.csv(output.rank,file=filename,row.names = FALSE)
        setwd(data.path)
        
        cat(sprintf("保存: %s (%d 匹配)\n", filename, nrow(output.rank)))
      }
      
      # 保存进度
      save_progress(i)
      total_processed <- total_processed + 1
      
      # 清理内存
      rm(score.matrix, output.rank, HNL.Q, ms2.Q)
      force_gc()
      
    }, error = function(e) {
      cat(sprintf("处理光谱 %d 时出错: %s\n", i, e$message))
    })
  }
  
  # 批次完成后检查内存
  if(check_memory("批次完成")) {
    cat("内存使用过高，将在下次重启时继续\n")
    restart_script <- sprintf('
# 自动重启脚本
cat("重启McSearch批处理...\\n")
source("McSearch_batch_smart.r")
')
    writeLines(restart_script, "restart_batch.r")
    cat("请运行: Rscript restart_batch.r\n")
    quit(save = "no")
  }
  
  cat(sprintf("批次 %d 完成\n", batch_count))
}

# 清理进度文件
if(file.exists(progress_file)) {
  file.remove(progress_file)
}

if(file.exists("restart_batch.r")) {
  file.remove("restart_batch.r")
}

cat(sprintf("\n=== 所有批次处理完成 ===\n"))
cat(sprintf("总共处理了 %d 个MS2光谱\n", total_processed))

# 统计输出文件
output_files <- list.files("output", pattern = "batch_output_.*\\.csv", full.names = FALSE)
cat(sprintf("生成了 %d 个结果文件\n", length(output_files)))

check_memory("全部完成")
