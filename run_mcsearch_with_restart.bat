@echo off
echo ========================================
echo McSearch 批处理器 - 自动重启版本
echo ========================================

REM 设置参数
set CHUNK_SIZE=5
set MAX_CHUNKS=15
set CURRENT_CHUNK=1

:LOOP
echo.
echo ========================================
echo 处理批次 %CURRENT_CHUNK%/%MAX_CHUNKS%
echo ========================================

REM 创建临时R脚本
echo # 临时批次处理脚本 > temp_batch_%CURRENT_CHUNK%.r
echo data.path ^<- getwd() >> temp_batch_%CURRENT_CHUNK%.r
echo db.name ^<- '<PERSON><PERSON>n HILIC_HNL library.msp' >> temp_batch_%CURRENT_CHUNK%.r
echo mzXMLfile.name ^<- 'test/test.mzXML' >> temp_batch_%CURRENT_CHUNK%.r
echo pre.tol ^<- 0.01 >> temp_batch_%CURRENT_CHUNK%.r
echo mass.range ^<- 200 >> temp_batch_%CURRENT_CHUNK%.r
echo mz.tol ^<- 0.01 >> temp_batch_%CURRENT_CHUNK%.r
echo topNo ^<- 30 >> temp_batch_%CURRENT_CHUNK%.r
echo HNL.threshold ^<- 36 >> temp_batch_%CURRENT_CHUNK%.r
echo ion.mode ^<- 'p' >> temp_batch_%CURRENT_CHUNK%.r
echo. >> temp_batch_%CURRENT_CHUNK%.r
echo chunk_size ^<- %CHUNK_SIZE% >> temp_batch_%CURRENT_CHUNK%.r
echo current_chunk ^<- %CURRENT_CHUNK% >> temp_batch_%CURRENT_CHUNK%.r
echo. >> temp_batch_%CURRENT_CHUNK%.r
echo cat("批次", current_chunk, "开始\n") >> temp_batch_%CURRENT_CHUNK%.r
echo. >> temp_batch_%CURRENT_CHUNK%.r
echo # 内存监控 >> temp_batch_%CURRENT_CHUNK%.r
echo monitor_memory ^<- function(step) { >> temp_batch_%CURRENT_CHUNK%.r
echo   mem_info ^<- gc() >> temp_batch_%CURRENT_CHUNK%.r
echo   used_mb ^<- sum(mem_info[,2]) >> temp_batch_%CURRENT_CHUNK%.r
echo   cat(sprintf("[内存] %%s: %%.1f MB\n", step, used_mb)) >> temp_batch_%CURRENT_CHUNK%.r
echo } >> temp_batch_%CURRENT_CHUNK%.r
echo. >> temp_batch_%CURRENT_CHUNK%.r
echo # 加载包 >> temp_batch_%CURRENT_CHUNK%.r
echo suppressMessages({ >> temp_batch_%CURRENT_CHUNK%.r
echo   library('readMzXmlData') >> temp_batch_%CURRENT_CHUNK%.r
echo   library('clue') >> temp_batch_%CURRENT_CHUNK%.r
echo   library('metaMS') >> temp_batch_%CURRENT_CHUNK%.r
echo   library('CHNOSZ') >> temp_batch_%CURRENT_CHUNK%.r
echo   library('stringr') >> temp_batch_%CURRENT_CHUNK%.r
echo   library('dplyr') >> temp_batch_%CURRENT_CHUNK%.r
echo }) >> temp_batch_%CURRENT_CHUNK%.r
echo. >> temp_batch_%CURRENT_CHUNK%.r
echo monitor_memory("包加载完成") >> temp_batch_%CURRENT_CHUNK%.r
echo. >> temp_batch_%CURRENT_CHUNK%.r
echo # 读取数据 >> temp_batch_%CURRENT_CHUNK%.r
echo data ^<- readMzXmlFile(mzXMLfile.name) >> temp_batch_%CURRENT_CHUNK%.r
echo ms2_indices ^<- which(sapply(data, function(x) x$metaData$msLevel == 2 ^&^& length(x$spectrum$mass) ^> 0)) >> temp_batch_%CURRENT_CHUNK%.r
echo cat(sprintf("找到 %%d 个MS2光谱\n", length(ms2_indices))) >> temp_batch_%CURRENT_CHUNK%.r
echo monitor_memory("数据读取完成") >> temp_batch_%CURRENT_CHUNK%.r
echo. >> temp_batch_%CURRENT_CHUNK%.r
echo # 读取数据库 >> temp_batch_%CURRENT_CHUNK%.r
echo setwd(paste0(data.path, '/files')) >> temp_batch_%CURRENT_CHUNK%.r
echo database ^<- read.msp(db.name, only.org = FALSE, org.set = c('C','H','N','O','P','S','F','Cl','Br','I'), noNumbers = NULL) >> temp_batch_%CURRENT_CHUNK%.r
echo bad.spectra ^<- read.csv(paste0('low quality spectra indices_', substring(db.name,1,regexpr("\\\\.",db.name)-1), '.csv'), stringsAsFactors = FALSE) >> temp_batch_%CURRENT_CHUNK%.r
echo bad.No ^<- bad.spectra[,1] >> temp_batch_%CURRENT_CHUNK%.r
echo setwd(data.path) >> temp_batch_%CURRENT_CHUNK%.r
echo monitor_memory("数据库读取完成") >> temp_batch_%CURRENT_CHUNK%.r
echo. >> temp_batch_%CURRENT_CHUNK%.r
echo # 计算当前批次的光谱范围 >> temp_batch_%CURRENT_CHUNK%.r
echo start_idx ^<- (current_chunk - 1) * chunk_size + 1 >> temp_batch_%CURRENT_CHUNK%.r
echo end_idx ^<- min(current_chunk * chunk_size, length(ms2_indices)) >> temp_batch_%CURRENT_CHUNK%.r
echo. >> temp_batch_%CURRENT_CHUNK%.r
echo if(start_idx ^<= length(ms2_indices)) { >> temp_batch_%CURRENT_CHUNK%.r
echo   cat(sprintf("处理光谱 %%d 到 %%d\n", start_idx, end_idx)) >> temp_batch_%CURRENT_CHUNK%.r
echo   source('process_spectra_chunk.r') >> temp_batch_%CURRENT_CHUNK%.r
echo } else { >> temp_batch_%CURRENT_CHUNK%.r
echo   cat("所有光谱已处理完成\n") >> temp_batch_%CURRENT_CHUNK%.r
echo } >> temp_batch_%CURRENT_CHUNK%.r

REM 运行R脚本
echo 运行 R 脚本...
Rscript temp_batch_%CURRENT_CHUNK%.r

REM 检查是否成功
if %ERRORLEVEL% NEQ 0 (
    echo 批次 %CURRENT_CHUNK% 出现错误，错误代码: %ERRORLEVEL%
    pause
    goto END
)

echo 批次 %CURRENT_CHUNK% 完成

REM 删除临时文件
del temp_batch_%CURRENT_CHUNK%.r

REM 增加批次计数
set /a CURRENT_CHUNK+=1

REM 检查是否还有更多批次
if %CURRENT_CHUNK% LEQ %MAX_CHUNKS% (
    echo 等待3秒后处理下一批次...
    timeout /t 3 /nobreak > nul
    goto LOOP
)

:END
echo.
echo ========================================
echo 所有批次处理完成！
echo ========================================

REM 统计结果文件
echo 统计输出文件...
dir output\*.csv /b | find /c ".csv" > temp_count.txt
set /p FILE_COUNT=<temp_count.txt
del temp_count.txt

echo 生成了 %FILE_COUNT% 个结果文件

pause
