
#########################################
# 批处理脚本 - 批次 19 到 21
#########################################

# 参数设置
data.path <- getwd()
db.name <- "Fiehn HILIC_HNL library.msp"
mzXMLfile.name <- "test/test.mzXML"
pre.tol <- 0.01
mass.range <- 200
mz.tol <- 0.01
topNo <- 30
HNL.threshold <- 36
ion.mode <- "p"
max_db_entries <- 500

# 目标光谱索引
target_indices <- c(392,394,396)

cat("批次 19-21 开始\n")

# 内存监控
monitor_memory <- function(step) {
  mem_info <- gc()
  used_mb <- sum(mem_info[,2])
  cat(sprintf("[内存] %s: %.1f MB\n", step, used_mb))
  if(used_mb > 1000) {
    for(i in 1:3) gc()
  }
}

# 加载包
suppressMessages({
  library("readMzXmlData")
  library("clue") 
  library("metaMS")
  library("CHNOSZ")
  library("stringr")
  library("dplyr")
})

monitor_memory("包加载完成")

# 读取数据
data <- readMzXmlFile(mzXMLfile.name)
monitor_memory("mzXML读取完成")

# 读取数据库
setwd(paste0(data.path, "/files"))
database <- read.msp(db.name, only.org = FALSE,
                    org.set = c("C","H","N","O","P","S","F","Cl","Br","I"), 
                    noNumbers = NULL)
bad.spectra <- read.csv(paste0("low quality spectra indices_",
                              substring(db.name,1,regexpr("\\.",db.name)-1),".csv"),
                       stringsAsFactors = FALSE)
bad.No <- bad.spectra[,1]
setwd(data.path)

monitor_memory("数据库读取完成")

# 简化CSS评分函数
simple_css_score <- function(HNL.q, ms2.l) {
  if(nrow(ms2.l) > topNo) {
    ms2.l <- ms2.l[order(-ms2.l[,2]),][1:topNo,]
  }
  
  matches <- 0
  for(i in 1:nrow(HNL.q)) {
    mz_diff <- abs(HNL.q[i,1] - ms2.l[,1])
    if(min(mz_diff) <= mz.tol) {
      matches <- matches + 1
    }
  }
  
  score <- if(matches > 0 && nrow(ms2.l) > 0) matches / max(nrow(HNL.q), nrow(ms2.l)) else 0
  return(list(score, matches, nrow(ms2.l)))
}

# 处理每个光谱
for(spectrum_idx in target_indices) {
  tryCatch({
    cat(sprintf("处理光谱索引 %d\n", spectrum_idx))
    
    # 获取光谱信息
    premass.Q <- data[[spectrum_idx]]$metaData$precursorMz
    ms2.Q <- as.data.frame(cbind(data[[spectrum_idx]]$spectrum$mass, data[[spectrum_idx]]$spectrum$intensity))
    rt.Q <- data[[spectrum_idx]]$metaData$retentionTime
    
    # 计算质量
    if(ion.mode %in% c("P","p")) { 
      mass.Q <- premass.Q - 1.007276
      adduct <- "[M+H]+"
    } else { 
      mass.Q <- premass.Q + 1.007276
      adduct <- "[M-H]-"
    }
    
    # 预处理光谱
    ms2.Q[,2] <- 100 * ms2.Q[,2] / max(ms2.Q[,2])
    ms2.Q <- ms2.Q[ms2.Q[,2] >= 1,]
    ms2.Q <- ms2.Q[ms2.Q[,1] <= (premass.Q + mz.tol),]
    
    if(nrow(ms2.Q) > 20) {
      ms2.Q <- ms2.Q[order(-ms2.Q[,2]),][1:20,]
    }
    
    if(nrow(ms2.Q) < 2) next
    
    ms2.Q[,2] <- sqrt(ms2.Q[,2])
    
    # 添加伪峰
    if(ion.mode %in% c("P","p")) {
      ms2.Q <- rbind(c(1.007276, 0), ms2.Q)
    } else {
      ms2.Q <- rbind(c(-1.007276, 0), ms2.Q)
    }
    
    if(min(abs(ms2.Q[,1] - premass.Q)) > mz.tol) {
      ms2.Q <- rbind(ms2.Q, c(premass.Q, 0))
    }
    
    # 生成HNL矩阵
    HNL.Q <- data.frame(matrix(ncol=6))
    colnames(HNL.Q) <- c("HNL","mz.a","mz.b","int.a","int.b","HNL.int")
    h <- 1
    
    for(m in 1:(nrow(ms2.Q)-1)) {
      for(n in (m+1):nrow(ms2.Q)) {
        hnl_val <- ms2.Q[n,1] - ms2.Q[m,1]
        if(hnl_val >= HNL.threshold) {
          HNL.Q[h,1] <- hnl_val
          HNL.Q[h,2] <- ms2.Q[n,1]
          HNL.Q[h,3] <- ms2.Q[m,1]
          HNL.Q[h,4] <- ms2.Q[n,2]
          HNL.Q[h,5] <- ms2.Q[m,2]
          HNL.Q[h,6] <- if(m==1) ms2.Q[n,2] else 0.5*(ms2.Q[n,2] + ms2.Q[m,2])
          h <- h + 1
        }
      }
    }
    
    HNL.Q <- HNL.Q[complete.cases(HNL.Q),]
    if(nrow(HNL.Q) == 0) next
    
    if(nrow(HNL.Q) > topNo) {
      HNL.Q <- HNL.Q[order(-HNL.Q[,6]),][1:topNo,]
    }
    
    # 搜索数据库
    results <- data.frame()
    
    for(l in 1:min(length(database), max_db_entries)) {
      if(l %in% bad.No) next
      
      if(!is.null(database[[l]]$PrecursorMZ)) {
        if(abs(database[[l]]$PrecursorMZ - premass.Q) > mass.range) next
      }
      
      name.L <- ifelse(is.null(database[[l]]$Name), paste("Entry", l), database[[l]]$Name)
      formula.L <- ifelse(is.null(database[[l]]$Formula), "", database[[l]]$Formula)
      
      if(is.null(database[[l]]$pspectrum) || nrow(database[[l]]$pspectrum) == 0) next
      
      ms2.L <- as.data.frame(database[[l]]$pspectrum)
      if(nrow(ms2.L) == 0) next
      
      ms2.L[,2] <- 10 * ms2.L[,2] / max(ms2.L[,2])
      
      css_result <- simple_css_score(HNL.Q, ms2.L)
      
      if(css_result[[1]] > 0) {
        result_row <- data.frame(
          score = css_result[[1]],
          matches = css_result[[2]],
          lib_peaks = css_result[[3]],
          name = name.L,
          formula = formula.L,
          db_index = l,
          stringsAsFactors = FALSE
        )
        results <- rbind(results, result_row)
      }
    }
    
    if(nrow(results) > 0) {
      results <- results[order(-results$score),]
      if(nrow(results) > 20) results <- results[1:20,]
      
      setwd(paste0(data.path, "/output"))
      filename <- paste0("batch_output_premass", round(premass.Q, 5), "_rt", round(rt.Q, 2), ".csv")
      write.csv(results, file = filename, row.names = FALSE)
      setwd(data.path)
      
      cat(sprintf("保存: %s (%d 匹配)\n", filename, nrow(results)))
    }
    
    gc()
    
  }, error = function(e) {
    cat(sprintf("处理光谱 %d 时出错: %s\n", spectrum_idx, e$message))
  })
}

cat("批次完成\n")
monitor_memory("批次结束")

