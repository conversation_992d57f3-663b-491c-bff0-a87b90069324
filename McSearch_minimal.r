#########################################
# 最小化版本的McSearch - 专门解决内存崩溃问题
# 只处理前几个光谱，用于测试和调试
#########################################

# 参数设置
data.path <- getwd()
db.name <- '<PERSON>ehn HILIC_HNL library.msp'
mzXMLfile.name <- 'test/test.mzXML'
pre.tol <- 0.01
mass.range <- 200
mz.tol <- 0.01
topNo <- 20                    # 大幅减少
HNL.threshold <- 36
ion.mode <- 'p'

# 限制处理数量
MAX_SPECTRA_TO_PROCESS <- 3    # 只处理前3个MS2光谱
MAX_DB_ENTRIES <- 100          # 只搜索前100个数据库条目

cat("=== McSearch 最小化版本 ===\n")
cat(sprintf("将只处理前 %d 个MS2光谱\n", MAX_SPECTRA_TO_PROCESS))
cat(sprintf("将只搜索前 %d 个数据库条目\n", MAX_DB_ENTRIES))

# 强制垃圾回收函数
force_gc <- function() {
  for(i in 1:5) {
    gc(verbose = FALSE)
    Sys.sleep(0.1)
  }
}

# 内存监控
check_memory <- function(step) {
  mem_info <- gc(verbose = FALSE)
  used_mb <- sum(mem_info[,2])
  cat(sprintf("[内存] %s: %.1f MB\n", step, used_mb))
  
  if(used_mb > 800) {
    cat("内存使用过高，强制清理...\n")
    force_gc()
    return(TRUE)
  }
  return(FALSE)
}

# 安全加载包
cat("加载必要的R包...\n")
essential_packages <- c('readMzXmlData', 'clue', 'metaMS', 'CHNOSZ', 'stringr')

for(pkg in essential_packages) {
  tryCatch({
    suppressMessages(library(pkg, character.only = TRUE))
    cat(sprintf("✓ %s\n", pkg))
  }, error = function(e) {
    cat(sprintf("✗ %s: %s\n", pkg, e$message))
    stop(sprintf("无法加载包: %s", pkg))
  })
}

check_memory("包加载完成")

# 读取数据
cat("读取mzXML文件...\n")
tryCatch({
  data <- readMzXmlFile(mzXMLfile.name)
  cat(sprintf("读取了 %d 个光谱\n", length(data)))
  check_memory("mzXML读取完成")
}, error = function(e) {
  cat("读取mzXML失败:", e$message, "\n")
  stop("无法继续")
})

# 找到MS2光谱
ms2_indices <- c()
for(i in 1:length(data)) {
  if(data[[i]]$metaData$msLevel == 2 && length(data[[i]]$spectrum$mass) > 0) {
    ms2_indices <- c(ms2_indices, i)
    if(length(ms2_indices) >= MAX_SPECTRA_TO_PROCESS) break
  }
}

cat(sprintf("找到 %d 个MS2光谱，将处理前 %d 个\n", length(ms2_indices), min(length(ms2_indices), MAX_SPECTRA_TO_PROCESS)))

if(length(ms2_indices) == 0) {
  stop("未找到MS2光谱")
}

# 读取数据库（限制数量）
cat("读取数据库（限制版本）...\n")
setwd(paste0(data.path, '/files'))

tryCatch({
  database <- read.msp(db.name, only.org = FALSE,
                      org.set = c('C','H','N','O','P','S','F','Cl','Br','I'), 
                      noNumbers = NULL)
  
  # 限制数据库大小
  if(length(database) > MAX_DB_ENTRIES) {
    database <- database[1:MAX_DB_ENTRIES]
    cat(sprintf("数据库限制为前 %d 个条目\n", MAX_DB_ENTRIES))
  }
  
  bad.spectra <- read.csv(paste0('low quality spectra indices_',
                                substring(db.name,1,regexpr("\\.",db.name)-1),'.csv'),
                         stringsAsFactors = FALSE)
  bad.No <- bad.spectra[,1]
  
  cat(sprintf("数据库: %d 条目\n", length(database)))
  check_memory("数据库读取完成")
  
}, error = function(e) {
  cat("读取数据库失败:", e$message, "\n")
  stop("无法继续")
})

setwd(data.path)

# 元素表
element.table <- data.frame(matrix(0,ncol=2,nrow = 10))
colnames(element.table) <- c('element','number')
element.table[,1] <- c('C','H','N','O','P','S','F','Cl','Br','I')

# 简化的CSS评分函数
simple_css_score <- function(HNL.q, ms2.l) {
  if(nrow(ms2.l) > topNo) {
    ms2.l <- ms2.l[order(-ms2.l[,2]),][1:topNo,]
  }
  
  matches <- 0
  for(i in 1:nrow(HNL.q)) {
    mz_diff <- abs(HNL.q[i,1] - ms2.l[,1])
    if(min(mz_diff) <= mz.tol) {
      matches <- matches + 1
    }
  }
  
  score <- if(matches > 0 && nrow(ms2.l) > 0) matches / max(nrow(HNL.q), nrow(ms2.l)) else 0
  return(list(score, matches, nrow(ms2.l)))
}

# 处理光谱
cat("开始处理光谱...\n")
processed_count <- 0

for(spec_idx in 1:min(length(ms2_indices), MAX_SPECTRA_TO_PROCESS)) {
  i <- ms2_indices[spec_idx]
  
  cat(sprintf("\n=== 处理光谱 %d/%d (索引 %d) ===\n", spec_idx, min(length(ms2_indices), MAX_SPECTRA_TO_PROCESS), i))
  
  tryCatch({
    # 获取光谱信息
    premass.Q <- data[[i]]$metaData$precursorMz
    ms2.Q <- as.data.frame(cbind(data[[i]]$spectrum$mass, data[[i]]$spectrum$intensity))
    rt.Q <- data[[i]]$metaData$retentionTime
    
    cat(sprintf("前体质量: %.4f, 保留时间: %.2f, 峰数: %d\n", premass.Q, rt.Q, nrow(ms2.Q)))
    
    # 计算质量
    if(ion.mode %in% c('P','p')) { 
      mass.Q <- premass.Q - 1.007276
      adduct <- '[M+H]+'
    } else { 
      mass.Q <- premass.Q + 1.007276
      adduct <- '[M-H]-'
    }
    
    # 预处理光谱
    ms2.Q[,2] <- 100 * ms2.Q[,2] / max(ms2.Q[,2])
    ms2.Q <- ms2.Q[ms2.Q[,2] >= 1,]  # 1%阈值
    ms2.Q <- ms2.Q[ms2.Q[,1] <= (premass.Q + mz.tol),]
    
    if(nrow(ms2.Q) > 15) {
      ms2.Q <- ms2.Q[order(-ms2.Q[,2]),][1:15,]  # 只保留前15个峰
    }
    
    if(nrow(ms2.Q) < 2) {
      cat("峰数太少，跳过\n")
      next
    }
    
    ms2.Q[,2] <- sqrt(ms2.Q[,2])
    
    # 添加伪峰
    if(ion.mode %in% c('P','p')) {
      ms2.Q <- rbind(c(1.007276, 0), ms2.Q)
    } else {
      ms2.Q <- rbind(c(-1.007276, 0), ms2.Q)
    }
    
    if(min(abs(ms2.Q[,1] - premass.Q)) > mz.tol) {
      ms2.Q <- rbind(ms2.Q, c(premass.Q, 0))
    }
    
    cat(sprintf("预处理后峰数: %d\n", nrow(ms2.Q)))
    
    # 生成HNL矩阵
    HNL.Q <- data.frame(matrix(ncol=6))
    colnames(HNL.Q) <- c('HNL','mz.a','mz.b','int.a','int.b','HNL.int')
    h <- 1
    
    for(m in 1:(nrow(ms2.Q)-1)) {
      for(n in (m+1):nrow(ms2.Q)) {
        hnl_val <- ms2.Q[n,1] - ms2.Q[m,1]
        if(hnl_val >= HNL.threshold) {
          HNL.Q[h,1] <- hnl_val
          HNL.Q[h,2] <- ms2.Q[n,1]
          HNL.Q[h,3] <- ms2.Q[m,1]
          HNL.Q[h,4] <- ms2.Q[n,2]
          HNL.Q[h,5] <- ms2.Q[m,2]
          HNL.Q[h,6] <- if(m==1) ms2.Q[n,2] else 0.5*(ms2.Q[n,2] + ms2.Q[m,2])
          h <- h + 1
        }
      }
    }
    
    HNL.Q <- HNL.Q[complete.cases(HNL.Q),]
    
    if(nrow(HNL.Q) == 0) {
      cat("未生成有效HNL，跳过\n")
      next
    }
    
    # 限制HNL数量
    if(nrow(HNL.Q) > topNo) {
      HNL.Q <- HNL.Q[order(-HNL.Q[,6]),][1:topNo,]
    }
    
    cat(sprintf("生成HNL: %d 个\n", nrow(HNL.Q)))
    
    # 搜索数据库
    results <- data.frame()
    
    for(l in 1:length(database)) {
      if(l %in% bad.No) next
      
      # 简单的前体质量筛选
      if(!is.null(database[[l]]$PrecursorMZ)) {
        if(abs(database[[l]]$PrecursorMZ - premass.Q) > mass.range) next
      }
      
      # 获取基本信息
      name.L <- ifelse(is.null(database[[l]]$Name), paste("Entry", l), database[[l]]$Name)
      formula.L <- ifelse(is.null(database[[l]]$Formula), "", database[[l]]$Formula)
      
      # 获取光谱
      if(is.null(database[[l]]$pspectrum) || nrow(database[[l]]$pspectrum) == 0) next
      
      ms2.L <- as.data.frame(database[[l]]$pspectrum)
      if(nrow(ms2.L) == 0) next
      
      ms2.L[,2] <- 10 * ms2.L[,2] / max(ms2.L[,2])
      
      # 计算分数
      css_result <- simple_css_score(HNL.Q, ms2.L)
      
      if(css_result[[1]] > 0) {
        result_row <- data.frame(
          score = css_result[[1]],
          matches = css_result[[2]],
          lib_peaks = css_result[[3]],
          name = name.L,
          formula = formula.L,
          db_index = l,
          stringsAsFactors = FALSE
        )
        results <- rbind(results, result_row)
      }
    }
    
    if(nrow(results) > 0) {
      # 排序并限制结果
      results <- results[order(-results$score),]
      if(nrow(results) > 10) results <- results[1:10,]
      
      # 保存结果
      setwd(paste0(data.path, '/output'))
      filename <- paste0('minimal_output_premass', round(premass.Q, 5), '_rt', round(rt.Q, 2), '.csv')
      write.csv(results, file = filename, row.names = FALSE)
      setwd(data.path)
      
      cat(sprintf("保存结果: %s (%d 个匹配)\n", filename, nrow(results)))
      processed_count <- processed_count + 1
    } else {
      cat("未找到匹配结果\n")
    }
    
    # 清理内存
    rm(results, HNL.Q, ms2.Q)
    force_gc()
    check_memory(sprintf("完成光谱 %d", spec_idx))
    
  }, error = function(e) {
    cat(sprintf("处理光谱时出错: %s\n", e$message))
    force_gc()
  })
}

cat(sprintf("\n=== 处理完成 ===\n"))
cat(sprintf("成功处理了 %d 个光谱\n", processed_count))
check_memory("全部完成")
