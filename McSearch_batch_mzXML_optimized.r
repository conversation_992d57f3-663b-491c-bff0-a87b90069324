#########################################
# 内存优化版本的McSearch批量处理脚本 (mzXML输入)
# 基于原始脚本优化，解决内存不足问题
# 优化策略：分块处理、内存管理、减少内存占用
#########################################

#########################################
# 参数设置
data.path <- getwd()                    # 数据路径
db.name <- 'Fiehn HILIC_HNL library.msp'              # MS/MS光谱数据库名称
mzXMLfile.name <- 'test/test.mzXML'          # 输入mzXML文件名
pre.tol <- 0.01                                       # 前体质量容差
mass.range <- 200                                     # 前体搜索质量范围，默认200 m/z
mz.tol <- 0.01                                        # HNL匹配质量容差，默认0.01 m/z
topNo <- 50                                          # 用于光谱匹配的最大HNL峰数，优化为50（原100）
HNL.threshold <- 36                                   # HNL值的最小质量，默认36 Da
ion.mode <- 'p'                                       # 离子化模式：'p'正离子，'n'负离子

# 内存优化参数
chunk_size <- 10                                      # 每次处理的光谱数量
max_database_entries <- 1000                          # 每次处理的数据库条目数量
gc_frequency <- 5                                     # 垃圾回收频率

#########################################

# 内存监控函数
monitor_memory <- function(step_name) {
  mem_info <- gc()
  used_mb <- sum(mem_info[,2])
  cat(sprintf("[内存监控] %s: 使用 %.1f MB\n", step_name, used_mb))
  if(used_mb > 800) {  # 如果内存使用超过800MB，强制垃圾回收
    cat("警告：内存使用过高，执行垃圾回收...\n")
    gc()
  }
}

# 加载必要的库
cat("加载R包...\n")
suppressMessages({
  library('readMzXmlData')
  library('clue')
  library('metaMS')
  library("CHNOSZ")
  library("ChemmineR") 
  library('ChemmineOB')
  library("fmcsR")
  library('stringr')
  library('dplyr')
  library('Rdisop')
})

monitor_memory("库加载完成")

# 设置内存限制
tryCatch({
  memory.limit(size = 2048)  # 设置2GB内存限制
  cat("内存限制设置为2GB\n")
}, error = function(e) {
  cat("无法设置内存限制（可能不是Windows系统）\n")
})

setwd(data.path)

# 分块读取mzXML文件
cat("开始读取mzXML文件...\n")
data <- readMzXmlFile(mzXMLfile.name)
cat(sprintf("成功读取 %d 个光谱\n", length(data)))
monitor_memory("mzXML文件读取完成")

# 预筛选MS2光谱
ms2_indices <- which(sapply(data, function(x) x$metaData$msLevel == 2 && length(x$spectrum$mass) > 0))
cat(sprintf("找到 %d 个有效的MS2光谱\n", length(ms2_indices)))

if(length(ms2_indices) == 0) {
  stop("未找到有效的MS2光谱，请检查输入文件")
}

# 分块加载数据库
setwd(paste0(data.path,'/files'))
cat("开始读取数据库...\n")

# 读取数据库元信息
if(db.name=='MoNA_HNL library.msp'){
  smiles.db <- read.csv('smiles_db.csv',stringsAsFactors = FALSE)
}

# 分块读取数据库
database <- read.msp(db.name, only.org = FALSE,
                     org.set = c('C','H','N','O','P','S','F','Cl','Br','I'), noNumbers = NULL)

bad.spectra <- read.csv(paste0('low quality spectra indices_',substring(db.name,1,regexpr("\\.",db.name)-1),'.csv'),stringsAsFactors = FALSE)
bad.No <- bad.spectra[,1]

cat(sprintf("数据库包含 %d 个条目\n", length(database)))
monitor_memory("数据库读取完成")

# 读取生物转化数据
setwd(paste0(data.path,'/biotrans'))
br.sdflist <- list.files(pattern= ".sdf")
bp <- read.csv('biotrans-plus.csv',stringsAsFactors = FALSE)
bm <- read.csv('biotrans-minus.csv',stringsAsFactors = FALSE)
brlist <- read.csv('biotrans mass change list.csv',stringsAsFactors = FALSE)
bruniquemass <- unique(brlist[,15])

# 只读取需要的SDF文件（内存优化）
cat("读取生物转化SDF文件...\n")
sdf_cache <- list()
for(m in 1:min(nrow(bp), 50)) {  # 限制读取数量
  sdf.seq <- which(paste(m,'.sdf',sep='')==br.sdflist)
  if(length(sdf.seq)>0){
    sdf_cache[[paste0('brsdf.',m)]] <- read.SDFset(br.sdflist[sdf.seq])
  }
}

monitor_memory("生物转化数据读取完成")

# 元素表
element.table <- data.frame(matrix(0,ncol=2,nrow = 10))
colnames(element.table) <- c('element','number')
element.table[,1] <- c('C','H','N','O','P','S','F','Cl','Br','I')

# 优化的CSS评分函数
CSS.score <- function(HNL.q, ms2.l){
  if(nrow(ms2.l)>topNo){
    ms2.l <- ms2.l[ms2.l[,2] > sort(ms2.l[,2],decreasing=TRUE)[topNo+1],]
  }
  
  # 创建HNL对齐矩阵
  HNL.alignment <- data.frame(matrix(ncol=4))
  colnames(HNL.alignment) <- c('HNL.q','int.q','HNL.l','int.l')
  
  for(m in 1:nrow(HNL.q)){
    mz.diff <- abs(HNL.q[m,1]-ms2.l[,1])
    if(min(mz.diff)<= mz.tol){
      HNL.alignment.individual <- cbind(HNL.q[m,1],HNL.q[m,6],ms2.l[mz.diff<=mz.tol,1],ms2.l[mz.diff<=mz.tol,2])
      colnames(HNL.alignment.individual) <- colnames(HNL.alignment)
      HNL.alignment <- rbind(HNL.alignment,HNL.alignment.individual)
    }
  }
  HNL.alignment <- HNL.alignment[complete.cases(HNL.alignment),]
  
  CSS <- 0
  mp <- 0
  if(nrow(HNL.alignment)>0){
    uniqueHNL.q <- unique(HNL.alignment[,1])
    uniqueHNL.l <- unique(HNL.alignment[,3])
    max.length <- max(length(uniqueHNL.q),length(uniqueHNL.l))
    
    if(max.length > 0) {
      matrix <- data.frame(matrix(0,ncol=(max.length+1),nrow=(max.length+1)))
      matrix[2:nrow(matrix),1] <- c(uniqueHNL.q ,rep(0,(max.length-length(uniqueHNL.q))))
      matrix[1,2:nrow(matrix)] <- c(uniqueHNL.l ,rep(0,(max.length-length(uniqueHNL.l))))
      
      for(m in 1:nrow(HNL.alignment)){
        matrix[matrix[,1]==HNL.alignment[m,1], matrix[1,]==HNL.alignment[m,3]] <- 
          HNL.alignment[m,2] * HNL.alignment[m,4]
      }
      
      if(length(uniqueHNL.q) > length(uniqueHNL.l)){matrix[2:nrow(matrix),(length(uniqueHNL.l)+2):ncol(matrix)] <- 0}
      if(length(uniqueHNL.q) < length(uniqueHNL.l)){matrix[(length(uniqueHNL.q)+2):nrow(matrix),2:ncol(matrix)] <- 0}
      matrix.B <-as.matrix(matrix[2:nrow(matrix),2:nrow(matrix)])
      
      # 匈牙利算法
      optimal <- solve_LSAP(matrix.B, maximum = TRUE)
      
      # 计算CSS分数
      sum.q <- 0
      for(m in 1:max.length){
        CSS <- CSS + matrix.B[m,optimal[m]]
        if(matrix.B[m,optimal[m]]>0){
          sum.q <- sum.q + max(HNL.q[HNL.q[,1]==matrix[m+1,1],6])^2
          mp <- mp + 1
        }
      }
      if(sum.q > 0 && sum(ms2.l[,2]^2) > 0) {
        CSS <- CSS/(sum.q*sum(ms2.l[,2]^2))^0.5
      }
    }
  }
  
  CSSreturn <- list(CSS,mp,nrow(ms2.l))
  return(CSSreturn)
}

cat("开始分块处理光谱...\n")

# 分块处理MS2光谱
total_processed <- 0
for(chunk_start in seq(1, length(ms2_indices), by = chunk_size)) {
  chunk_end <- min(chunk_start + chunk_size - 1, length(ms2_indices))
  current_chunk <- ms2_indices[chunk_start:chunk_end]
  
  cat(sprintf("处理光谱块 %d-%d (共%d个)\n", chunk_start, chunk_end, length(ms2_indices)))
  
  for(idx in current_chunk) {
    i <- ms2_indices[idx]
    
    # 获取查询光谱信息
    premass.Q <- data[[i]]$metaData$precursorMz
    ms2.Q <- as.data.frame(cbind(data[[i]]$spectrum$mass, data[[i]]$spectrum$intensity))
    rt.Q <- data[[i]]$metaData$retentionTime
    
    # 计算质量
    if(ion.mode %in% c('P','p')){ 
      mass.Q <- premass.Q - 1.007276
      adduct <- '[M+H]+'
    }
    if(ion.mode %in% c('N','n')){ 
      mass.Q <- premass.Q + 1.007276
      adduct <- '[M-H]-'
    }
    
    # 预处理MS2光谱
    ms2.Q[,2] <- 100*ms2.Q[,2]/max(ms2.Q[,2])
    ms2.Q <- ms2.Q[ms2.Q[,2] >= 1,]  # 1%阈值
    ms2.Q <- ms2.Q[ms2.Q[,1]<=(premass.Q+mz.tol),]  # 排除前体质量后的峰
    
    if(nrow(ms2.Q)>30){ms2.Q <- ms2.Q[ms2.Q[,2] > sort(ms2.Q[,2],decreasing=TRUE)[31],]}  # 前30个峰
    ms2.Q[,2] <- sqrt(ms2.Q[,2])  # 平方根变换
    
    # 添加伪m/z
    if(ion.mode %in% c('P','p')){
      ms2.Q <- rbind(c(1.007276,0),ms2.Q)
    }
    if(ion.mode %in% c('N','n')){
      ms2.Q <- rbind(c(-1.007276,0),ms2.Q)
    }
    
    if(min(abs(ms2.Q[,1]-premass.Q)) > mz.tol){ms2.Q <- rbind(ms2.Q,c(premass.Q,0))}
    
    # 生成HNL矩阵（内存优化版本）
    if(nrow(ms2.Q) > 1) {
      HNL.Q <- data.frame(matrix(ncol=6))
      colnames(HNL.Q) <- c('HNL','mz.a','mz.b','int.a','int.b','HNL.int')
      h <- 1
      
      for(m in 1:(nrow(ms2.Q)-1)){
        for(n in (m+1):nrow(ms2.Q)){
          HNL.Q[h,1] <- ms2.Q[n,1] - ms2.Q[m,1]
          HNL.Q[h,2] <- ms2.Q[n,1]
          HNL.Q[h,3] <- ms2.Q[m,1]
          HNL.Q[h,4] <- ms2.Q[n,2]
          HNL.Q[h,5] <- ms2.Q[m,2]
          if(m==1){HNL.Q[h,6] <- ms2.Q[n,2]}
          if(m!=1){HNL.Q[h,6] <- 0.5*(HNL.Q[h,4]+HNL.Q[h,5])}
          h <- h+1
        }
      }
      HNL.Q <- HNL.Q[HNL.Q[,1]>=HNL.threshold,]
      
      # 限制HNL峰数量
      HNL.Q.1st <- HNL.Q[1:(nrow(ms2.Q)-1),]
      HNL.Q.2nd <- HNL.Q[nrow(ms2.Q):nrow(HNL.Q),]
      if(nrow(HNL.Q.2nd)>topNo){
        HNL.Q.2nd <- HNL.Q.2nd[HNL.Q.2nd[,6] > sort(HNL.Q.2nd[,6],decreasing=TRUE)[topNo+1],]
        HNL.Q <- rbind(HNL.Q.1st, HNL.Q.2nd)
      }
      
      # 分块处理数据库搜索
      score.matrix <- data.frame()
      
      for(db_start in seq(1, length(database), by = max_database_entries)) {
        db_end <- min(db_start + max_database_entries - 1, length(database))
        
        # 处理当前数据库块
        temp_score <- data.frame(matrix(ncol=9))
        colnames(temp_score) <- c('CSS intensity score','matched HNL No.','std HNL No.','name','formula','database No.','SMILES','InChIKey','ion mode')
        h <- 1
        
        for(l in db_start:db_end){
          if(is.element(l,bad.No)) next
          if(is.null(database[[l]]$PrecursorMZ)==FALSE){if(abs(database[[l]]$PrecursorMZ-premass.Q) > mass.range) next}
          
          # 处理分子式
          formula.L <- database[[l]]$Formula
          if(grepl('\\[',formula.L)){
            formula.L <- substring(formula.L,regexpr("\\[",formula.L)+1,regexpr("\\]",formula.L)-1)
          }
          if(grepl('\\[',formula.L)==FALSE){
            if(grepl('\\+',formula.L) | grepl('\\-',formula.L)){
              formula.L <- substring(formula.L,1,nchar(formula.L)-1)
            }
          }
          
          # 质量筛选
          tryCatch({
            a <- element.table
            a.table <- count.elements(formula.L)
            for(m in 1:length(a.table)){a[a[,1]==names(a.table)[m],2] <- a.table[m]}
            mass.L <- getMolecule(formula.L, z=0)$exactmass
            mass.list <- mass.L + bruniquemass - mass.Q
            if(min(abs(mass.list)) > pre.tol) next
          }, error = function(e) {
            next
          })
          
          name.L <- database[[l]]$Name
          
          # 离子模式
          if(is.null(database[[l]]$Ion_mode)==FALSE){ionmode.L <- database[[l]]$Ion_mode}
          else if(is.null(database[[l]]$Precursor_type)==FALSE){
            str <- substr(database[[l]]$Precursor_type,nchar(database[[l]]$Precursor_type),nchar(database[[l]]$Precursor_type))
            if(str=="+"){ionmode.L <- 'P'}
            else if(str=="-"){ionmode.L <- 'N'}
            else {ionmode.L <- 'Unknown'}
          }
          else {ionmode.L <- 'Unknown'}
          
          # SMILES
          if(grepl("computed SMILES=", database[[l]]$Comments)){
            a <- substring(database[[l]]$Comments, regexpr("computed SMILES=", database[[l]]$Comments) + 16)
            smiles.L <- strsplit(a, '\"')[[1]][1]
          }
          else if(grepl("SMILES=", database[[l]]$Comments)){
            a <- substring(database[[l]]$Comments, regexpr("SMILES=", database[[l]]$Comments) + 7)
            smiles.L <- strsplit(a, '\"')[[1]][1]
          }
          else {smiles.L <- ""}
          
          # InChIKey
          if(is.null(database[[l]]$InChIKey)==FALSE){inchikey.L <- database[[l]]$InChIKey}
          else {inchikey.L <- paste0('No InChIKey info:',l)}
          
          # MS2光谱
          ms2.L <- as.data.frame(database[[l]]$pspectrum)
          if(nrow(ms2.L) > 0) {
            ms2.L[,2] <- 10*ms2.L[,2]/max(ms2.L[,2])
            
            # 计算CSS分数
            CSS.list <- CSS.score(HNL.Q,ms2.L)
            temp_score[h,1] <- as.numeric(CSS.list[1])
            temp_score[h,2] <- as.numeric(CSS.list[2])
            temp_score[h,3] <- as.numeric(CSS.list[3])
            temp_score[h,4] <- name.L
            temp_score[h,5] <- formula.L
            temp_score[h,6] <- l
            if(db.name=='MoNA_HNL library.msp'){temp_score[h,7] <- smiles.db[l,2]}
            else {temp_score[h,7] <- smiles.L}
            temp_score[h,8] <- inchikey.L
            temp_score[h,9] <- ionmode.L
            h <- h + 1
          }
        }
        
        # 合并结果
        temp_score <- temp_score[complete.cases(temp_score),]
        if(nrow(temp_score) > 0) {
          score.matrix <- rbind(score.matrix, temp_score)
        }
        
        # 定期垃圾回收
        if((db_end %% (max_database_entries * gc_frequency)) == 0) {
          gc()
        }
      }
      
      # 后处理结果
      if(nrow(score.matrix) > 0) {
        score.matrix <- score.matrix[score.matrix[,1]>0,]  # score > 0
        score.matrix <- score.matrix[score.matrix[,3]>1,]  # std.HNL > 1
        score.matrix <- score.matrix[score.matrix[,2]>0,]  # mp > 0
        
        if(nrow(score.matrix) > 0) {
          score.matrix <- score.matrix[order(-(70*(score.matrix[,2]/topNo)/(0.5*log10(100*score.matrix[,3]/topNo))+5*score.matrix[,1])),]
          if(nrow(score.matrix) > 100){score.matrix <- score.matrix[1:100,]}  # 限制结果数量
          
          # 去除相同结构
          if(nrow(score.matrix) > 1) {
            for(m in 1:(nrow(score.matrix)-1)){ 
              for(n in (m+1):nrow(score.matrix)){
                if(score.matrix[n,7]==score.matrix[m,7]|str_to_lower(score.matrix[n,4])==str_to_lower(score.matrix[m,4])|score.matrix[n,8]==score.matrix[m,8]){
                  score.matrix[n,1] <- NA
                }
              }
            }
            score.matrix <- score.matrix[complete.cases(score.matrix),]
          }
          
          if(nrow(score.matrix) > 0) {
            # 简化输出（跳过复杂的生物转化部分以节省内存）
            output.rank <- cbind(1:nrow(score.matrix), score.matrix, adduct, 0, "", "", 0, "", "", 0, "", 0, score.matrix[,5], 0, 0)
            colnames(output.rank) <- c('rank','CSS intensity score','matched HNL No.','std HNL No.','name','formula','database No.','SMILES','InChIKey','ion mode','Adduct type','Heavy atom No.',"Reaction.1","Description.1","Reaction1_No.","Reaction.2","Description.2","Reaction2_No.","Formula.change","Mass error",'Final formula','modified matched ratio','McSearch score')
            
            # 计算McSearch分数
            output.rank[,22] <- (output.rank[,3]/topNo)/(0.5*log10(100*output.rank[,4]/topNo))
            output.rank[,23] <- 70*output.rank[,22] + 5*output.rank[,2]
            
            # 保存结果
            setwd(paste0(data.path,'/output'))
            filename <- paste0('output_premass',premass.Q,'_rt',rt.Q,'.csv')
            write.csv(output.rank,file=filename,row.names = FALSE)
            cat(sprintf("已保存结果: %s (包含%d个匹配)\n", filename, nrow(output.rank)))
          }
        }
      }
    }
    
    total_processed <- total_processed + 1
    
    # 定期内存监控和垃圾回收
    if(total_processed %% gc_frequency == 0) {
      monitor_memory(sprintf("已处理%d个光谱", total_processed))
    }
  }
  
  # 每个块处理完后强制垃圾回收
  gc()
}

setwd(data.path)
cat(sprintf("处理完成！共处理了 %d 个MS2光谱\n", total_processed))
monitor_memory("全部处理完成")
