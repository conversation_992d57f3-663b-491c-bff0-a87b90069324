#########################################
# 简化版内存优化McSearch脚本
# 专注于解决内存崩溃问题，简化处理流程
#########################################

# 参数设置
data.path <- getwd()
db.name <- 'Fiehn HILIC_HNL library.msp'
mzXMLfile.name <- 'test/test.mzXML'
pre.tol <- 0.01
mass.range <- 200
mz.tol <- 0.01
topNo <- 30                    # 大幅减少到30
HNL.threshold <- 36
ion.mode <- 'p'

# 内存优化参数
max_spectra_per_run <- 5       # 每次只处理5个光谱
max_db_entries <- 500          # 每次只搜索500个数据库条目

cat("=== 简化版McSearch开始 ===\n")

# 内存监控
monitor_memory <- function(step) {
  mem_info <- gc()
  used_mb <- sum(mem_info[,2])
  cat(sprintf("[内存] %s: %.1f MB\n", step, used_mb))
  if(used_mb > 1000) {
    cat("警告：内存使用过高，强制垃圾回收\n")
    for(i in 1:3) gc()  # 多次垃圾回收
  }
}

# 安全加载包
safe_load_packages <- function() {
  packages <- c('readMzXmlData', 'clue', 'metaMS', 'CHNOSZ', 'stringr', 'dplyr')
  
  for(pkg in packages) {
    tryCatch({
      suppressMessages(library(pkg, character.only = TRUE))
      cat(sprintf("✓ %s\n", pkg))
    }, error = function(e) {
      cat(sprintf("✗ %s: %s\n", pkg, e$message))
      stop(sprintf("无法加载必需的包: %s", pkg))
    })
  }
}

# 安全读取数据
safe_read_data <- function() {
  tryCatch({
    cat("读取mzXML文件...\n")
    data <- readMzXmlFile(mzXMLfile.name)
    cat(sprintf("读取了 %d 个光谱\n", length(data)))
    
    # 立即筛选MS2光谱
    ms2_indices <- c()
    for(i in 1:length(data)) {
      if(data[[i]]$metaData$msLevel == 2 && length(data[[i]]$spectrum$mass) > 0) {
        ms2_indices <- c(ms2_indices, i)
      }
    }
    
    cat(sprintf("找到 %d 个MS2光谱\n", length(ms2_indices)))
    
    if(length(ms2_indices) == 0) {
      stop("未找到有效的MS2光谱")
    }
    
    return(list(data = data, ms2_indices = ms2_indices))
    
  }, error = function(e) {
    cat(sprintf("读取数据时出错: %s\n", e$message))
    stop(e)
  })
}

# 安全读取数据库
safe_read_database <- function() {
  tryCatch({
    cat("读取数据库...\n")
    setwd(paste0(data.path, '/files'))
    
    database <- read.msp(db.name, only.org = FALSE,
                        org.set = c('C','H','N','O','P','S','F','Cl','Br','I'), 
                        noNumbers = NULL)
    
    bad.spectra <- read.csv(paste0('low quality spectra indices_',
                                  substring(db.name,1,regexpr("\\.",db.name)-1),'.csv'),
                           stringsAsFactors = FALSE)
    bad.No <- bad.spectra[,1]
    
    cat(sprintf("数据库: %d 条目, 低质量: %d 条目\n", length(database), length(bad.No)))
    
    setwd(data.path)
    return(list(database = database, bad.No = bad.No))
    
  }, error = function(e) {
    cat(sprintf("读取数据库时出错: %s\n", e$message))
    setwd(data.path)
    stop(e)
  })
}

# 简化的CSS评分函数
simple_css_score <- function(HNL.q, ms2.l) {
  if(nrow(ms2.l) > topNo) {
    ms2.l <- ms2.l[order(-ms2.l[,2]),][1:topNo,]
  }
  
  # 简单的匹配计数
  matches <- 0
  total_intensity <- 0
  
  for(i in 1:nrow(HNL.q)) {
    mz_diff <- abs(HNL.q[i,1] - ms2.l[,1])
    if(min(mz_diff) <= mz.tol) {
      matches <- matches + 1
      total_intensity <- total_intensity + HNL.q[i,6]
    }
  }
  
  # 简化的分数计算
  if(matches > 0 && nrow(ms2.l) > 0) {
    score <- matches / max(nrow(HNL.q), nrow(ms2.l))
  } else {
    score <- 0
  }
  
  return(list(score, matches, nrow(ms2.l)))
}

# 处理单个光谱
process_single_spectrum <- function(spectrum_data, database_info, spectrum_index) {
  tryCatch({
    data <- spectrum_data$data
    i <- spectrum_data$ms2_indices[spectrum_index]
    database <- database_info$database
    bad.No <- database_info$bad.No
    
    cat(sprintf("处理光谱 %d/%d (索引 %d)\n", spectrum_index, length(spectrum_data$ms2_indices), i))
    
    # 获取光谱信息
    premass.Q <- data[[i]]$metaData$precursorMz
    ms2.Q <- as.data.frame(cbind(data[[i]]$spectrum$mass, data[[i]]$spectrum$intensity))
    rt.Q <- data[[i]]$metaData$retentionTime
    
    # 计算质量
    if(ion.mode %in% c('P','p')) { 
      mass.Q <- premass.Q - 1.007276
      adduct <- '[M+H]+'
    } else { 
      mass.Q <- premass.Q + 1.007276
      adduct <- '[M-H]-'
    }
    
    # 预处理光谱
    ms2.Q[,2] <- 100 * ms2.Q[,2] / max(ms2.Q[,2])
    ms2.Q <- ms2.Q[ms2.Q[,2] >= 1,]  # 1%阈值
    ms2.Q <- ms2.Q[ms2.Q[,1] <= (premass.Q + mz.tol),]
    
    if(nrow(ms2.Q) > 20) {
      ms2.Q <- ms2.Q[order(-ms2.Q[,2]),][1:20,]  # 只保留前20个峰
    }
    
    if(nrow(ms2.Q) < 2) {
      cat("光谱峰数太少，跳过\n")
      return(NULL)
    }
    
    ms2.Q[,2] <- sqrt(ms2.Q[,2])
    
    # 添加伪峰
    if(ion.mode %in% c('P','p')) {
      ms2.Q <- rbind(c(1.007276, 0), ms2.Q)
    } else {
      ms2.Q <- rbind(c(-1.007276, 0), ms2.Q)
    }
    
    if(min(abs(ms2.Q[,1] - premass.Q)) > mz.tol) {
      ms2.Q <- rbind(ms2.Q, c(premass.Q, 0))
    }
    
    # 生成HNL矩阵（简化版）
    HNL.Q <- data.frame(matrix(ncol=6))
    colnames(HNL.Q) <- c('HNL','mz.a','mz.b','int.a','int.b','HNL.int')
    h <- 1
    
    for(m in 1:(nrow(ms2.Q)-1)) {
      for(n in (m+1):nrow(ms2.Q)) {
        hnl_val <- ms2.Q[n,1] - ms2.Q[m,1]
        if(hnl_val >= HNL.threshold) {
          HNL.Q[h,1] <- hnl_val
          HNL.Q[h,2] <- ms2.Q[n,1]
          HNL.Q[h,3] <- ms2.Q[m,1]
          HNL.Q[h,4] <- ms2.Q[n,2]
          HNL.Q[h,5] <- ms2.Q[m,2]
          HNL.Q[h,6] <- if(m==1) ms2.Q[n,2] else 0.5*(ms2.Q[n,2] + ms2.Q[m,2])
          h <- h + 1
        }
      }
    }
    
    HNL.Q <- HNL.Q[complete.cases(HNL.Q),]
    
    if(nrow(HNL.Q) == 0) {
      cat("未生成有效的HNL，跳过\n")
      return(NULL)
    }
    
    # 限制HNL数量
    if(nrow(HNL.Q) > topNo) {
      HNL.Q <- HNL.Q[order(-HNL.Q[,6]),][1:topNo,]
    }
    
    cat(sprintf("生成了 %d 个HNL\n", nrow(HNL.Q)))
    
    # 搜索数据库（分批处理）
    results <- data.frame()
    
    db_batches <- seq(1, min(length(database), max_db_entries), by = 100)
    
    for(batch_start in db_batches) {
      batch_end <- min(batch_start + 99, length(database), max_db_entries)
      
      for(l in batch_start:batch_end) {
        if(l %in% bad.No) next
        
        # 简单的前体质量筛选
        if(!is.null(database[[l]]$PrecursorMZ)) {
          if(abs(database[[l]]$PrecursorMZ - premass.Q) > mass.range) next
        }
        
        # 获取基本信息
        name.L <- ifelse(is.null(database[[l]]$Name), paste("Entry", l), database[[l]]$Name)
        formula.L <- ifelse(is.null(database[[l]]$Formula), "", database[[l]]$Formula)
        
        # 获取光谱
        if(is.null(database[[l]]$pspectrum) || nrow(database[[l]]$pspectrum) == 0) next
        
        ms2.L <- as.data.frame(database[[l]]$pspectrum)
        if(nrow(ms2.L) == 0) next
        
        ms2.L[,2] <- 10 * ms2.L[,2] / max(ms2.L[,2])
        
        # 计算简化分数
        css_result <- simple_css_score(HNL.Q, ms2.L)
        
        if(css_result[[1]] > 0) {
          result_row <- data.frame(
            score = css_result[[1]],
            matches = css_result[[2]],
            lib_peaks = css_result[[3]],
            name = name.L,
            formula = formula.L,
            db_index = l,
            stringsAsFactors = FALSE
          )
          results <- rbind(results, result_row)
        }
      }
      
      # 每批次后垃圾回收
      if(nrow(results) > 0 && batch_end %% 300 == 0) {
        gc()
      }
    }
    
    if(nrow(results) > 0) {
      # 排序并限制结果
      results <- results[order(-results$score),]
      if(nrow(results) > 20) results <- results[1:20,]
      
      # 保存结果
      setwd(paste0(data.path, '/output'))
      filename <- paste0('simple_output_premass', round(premass.Q, 5), '_rt', round(rt.Q, 2), '.csv')
      write.csv(results, file = filename, row.names = FALSE)
      setwd(data.path)
      
      cat(sprintf("保存结果: %s (%d 个匹配)\n", filename, nrow(results)))
      return(filename)
    } else {
      cat("未找到匹配结果\n")
      return(NULL)
    }
    
  }, error = function(e) {
    cat(sprintf("处理光谱时出错: %s\n", e$message))
    return(NULL)
  })
}

# 主函数
main <- function() {
  tryCatch({
    monitor_memory("开始")
    
    # 加载包
    safe_load_packages()
    monitor_memory("包加载完成")
    
    # 读取数据
    spectrum_data <- safe_read_data()
    monitor_memory("光谱数据读取完成")
    
    database_info <- safe_read_database()
    monitor_memory("数据库读取完成")
    
    # 处理光谱（每次只处理几个）
    total_spectra <- length(spectrum_data$ms2_indices)
    processed_count <- 0
    success_count <- 0
    
    # 限制处理数量以避免内存问题
    max_to_process <- min(total_spectra, max_spectra_per_run)
    
    cat(sprintf("开始处理 %d 个光谱（共 %d 个）\n", max_to_process, total_spectra))
    
    for(i in 1:max_to_process) {
      cat(sprintf("\n=== 处理第 %d 个光谱 ===\n", i))
      
      result <- process_single_spectrum(spectrum_data, database_info, i)
      processed_count <- processed_count + 1
      
      if(!is.null(result)) {
        success_count <- success_count + 1
      }
      
      # 每处理一个光谱就清理内存
      gc()
      monitor_memory(sprintf("完成第 %d 个光谱", i))
      
      # 如果内存使用过高，停止处理
      mem_info <- gc()
      if(sum(mem_info[,2]) > 1200) {
        cat("内存使用过高，停止处理更多光谱\n")
        break
      }
    }
    
    cat(sprintf("\n=== 处理完成 ===\n"))
    cat(sprintf("处理了 %d 个光谱，成功 %d 个\n", processed_count, success_count))
    monitor_memory("全部完成")
    
  }, error = function(e) {
    cat(sprintf("主程序出错: %s\n", e$message))
    cat("错误详情:\n")
    print(traceback())
  })
}

# 运行主程序
main()
